# 微信H5项目部署和调试指南

## 🚨 当前问题分析

### 问题1：微信安全提示
**现象**: 微信提示"将要访问...无法确认该网页的安全性"
**原因**: 缺少微信域名验证文件
**解决方案**: 配置微信域名验证

### 问题2：页面空白
**现象**: `https://m.dboss.pro/store-wechat-bind` 页面空白
**可能原因**:
1. wechat-h5项目未正确部署到 `m.dboss.pro`
2. 构建配置问题（已修复publicPath）
3. 路由配置问题
4. 静态资源加载失败

## 🛠️ 解决步骤

### 步骤1：修复构建配置（已完成）
```javascript
// vue.config.js - 已修复
publicPath: '/', // 改为根路径部署
```

### 步骤2：配置微信域名验证

#### 2.1 获取验证文件
1. 登录微信公众平台
2. 进入"设置与开发" → "公众号设置" → "功能设置"
3. 在"业务域名"中添加 `m.dboss.pro`
4. 下载验证文件（格式：`MP_verify_xxxxxxxxxx.txt`）

#### 2.2 部署验证文件
```bash
# 将验证文件放到 wechat-h5/public/ 目录下
cp MP_verify_xxxxxxxxxx.txt wechat-h5/public/
```

### 步骤3：构建和部署wechat-h5项目

#### 3.1 本地构建
```bash
cd wechat-h5

# 安装依赖（如果还没安装）
yarn install

# 构建生产版本
yarn build

# 检查构建结果
ls -la dist/
```

#### 3.2 部署到服务器
```bash
# 将dist目录内容部署到 m.dboss.pro 的根目录
# 确保以下文件可以访问：
# https://m.dboss.pro/index.html
# https://m.dboss.pro/static/js/app.xxx.js
# https://m.dboss.pro/MP_verify_xxxxxxxxxx.txt
```

#### 3.3 Nginx配置示例
```nginx
server {
    listen 80;
    listen 443 ssl;
    server_name m.dboss.pro;
    
    # SSL配置
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;
    
    # 强制HTTPS
    if ($scheme != "https") {
        return 301 https://$server_name$request_uri;
    }
    
    root /var/www/wechat-h5;
    index index.html;
    
    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass https://store-api.dboss.pro;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态资源缓存
    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 微信验证文件
    location ~ ^/MP_verify_.*\.txt$ {
        root /var/www/wechat-h5;
    }
}
```

### 步骤4：测试验证

#### 4.1 基础访问测试
```bash
# 测试基础页面访问
curl -I https://m.dboss.pro/
curl -I https://m.dboss.pro/test
curl -I https://m.dboss.pro/store-wechat-bind

# 测试微信验证文件
curl https://m.dboss.pro/MP_verify_xxxxxxxxxx.txt
```

#### 4.2 功能测试
1. **测试页面**: 访问 `https://m.dboss.pro/test`
2. **绑定页面**: 访问 `https://m.dboss.pro/store-wechat-bind?token=test&type=WECHAT_OFFIACCOUNT_OPEN_ID`
3. **错误页面**: 访问 `https://m.dboss.pro/error`

#### 4.3 微信环境测试
1. 在微信中访问测试页面
2. 检查是否还有安全提示
3. 测试页面功能是否正常

## 🔧 调试工具

### 1. 本地调试
```bash
# 启动开发服务器
cd wechat-h5
yarn serve

# 访问测试页面
http://localhost:8080/test
http://localhost:8080/store-wechat-bind?token=test&type=WECHAT_OFFIACCOUNT_OPEN_ID
```

### 2. 生产环境调试
```bash
# 检查页面是否可访问
curl -v https://m.dboss.pro/

# 检查静态资源
curl -v https://m.dboss.pro/static/js/app.xxx.js

# 检查API代理
curl -v https://m.dboss.pro/api/store/wechat-bind/status
```

### 3. 微信开发者工具
1. 打开微信开发者工具
2. 选择"公众号网页调试"
3. 输入URL: `https://m.dboss.pro/test`
4. 检查控制台错误信息

## 📋 检查清单

### 部署前检查
- [ ] wechat-h5项目构建成功
- [ ] 构建产物包含所有必要文件
- [ ] 环境变量配置正确
- [ ] 微信验证文件已准备

### 部署后检查
- [ ] 基础页面可以访问
- [ ] 静态资源加载正常
- [ ] API代理工作正常
- [ ] 微信验证文件可访问
- [ ] HTTPS证书有效

### 功能检查
- [ ] 测试页面显示正常
- [ ] 绑定确认页面可以打开
- [ ] 错误页面显示正常
- [ ] 微信环境检测正常
- [ ] URL参数解析正常

## 🚨 常见问题

### 1. 页面空白
**检查项**:
- 浏览器控制台是否有错误
- 静态资源是否加载成功
- API请求是否正常

### 2. 微信安全提示
**检查项**:
- 域名是否在微信公众号后台配置
- 验证文件是否正确部署
- 域名是否使用HTTPS

### 3. 路由不工作
**检查项**:
- Nginx配置是否支持history模式
- 路由配置是否正确
- 基础路径配置是否正确

## 📞 紧急处理

如果问题紧急，可以临时使用以下方案：

### 临时方案1：使用子路径部署
```javascript
// vue.config.js
publicPath: '/wechat/',
```
然后部署到 `https://store.dboss.pro/wechat/`

### 临时方案2：使用原域名
暂时将H5项目部署到 `https://store.dboss.pro/h5/`

## 📈 监控建议

1. **错误监控**: 添加前端错误监控
2. **性能监控**: 监控页面加载时间
3. **访问统计**: 统计页面访问量和转化率
4. **微信环境**: 监控不同微信版本的兼容性
