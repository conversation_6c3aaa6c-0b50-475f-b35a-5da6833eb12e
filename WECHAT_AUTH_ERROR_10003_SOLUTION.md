# 微信授权错误10003解决方案

## 🚨 错误信息
```
微信登录失败，redirect_uri域名与后台配置不一致，错误码：10003
```

## 🔍 问题原因
错误码10003表示`redirect_uri`参数中的域名与微信公众号后台配置的"网页授权域名"不一致。

## ✅ 已修复的代码问题

### 1. redirect_uri构建方式优化
**修复前**:
```javascript
const currentUrl = window.location.href.split('?')[0]
const redirectUri = encodeURIComponent(currentUrl + `?token=${this.token}&type=${this.unionType}`)
```

**修复后**:
```javascript
const protocol = window.location.protocol
const host = window.location.host
const pathname = window.location.pathname
const redirectUri = encodeURIComponent(`${protocol}//${host}${pathname}`)
```

### 2. 参数传递方式优化
**修复前**: 通过URL参数传递token和type
**修复后**: 通过sessionStorage传递，避免URL参数干扰

## 🔧 微信公众号后台配置步骤

### 步骤1: 登录微信公众平台
1. 访问 [https://mp.weixin.qq.com](https://mp.weixin.qq.com)
2. 使用管理员账号登录

### 步骤2: 配置网页授权域名
1. 进入"设置与开发" → "公众号设置" → "功能设置"
2. 找到"网页授权域名"设置项
3. 点击"设置"按钮

### 步骤3: 添加授权域名
**重要**: 域名格式必须正确！

✅ **正确格式**:
```
m.dboss.pro
```

❌ **错误格式**:
```
https://m.dboss.pro          # 不要包含协议
http://m.dboss.pro           # 不要包含协议
m.dboss.pro/                 # 不要包含路径
m.dboss.pro/bind-confirm     # 不要包含路径
www.m.dboss.pro              # 确保域名正确
```

### 步骤4: 下载并上传验证文件
1. 在域名配置页面下载验证文件（格式：`MP_verify_xxxxxxxxxx.txt`）
2. 将验证文件上传到域名根目录
3. 确保文件可以通过以下URL访问：
   ```
   https://m.dboss.pro/MP_verify_xxxxxxxxxx.txt
   ```

### 步骤5: 保存配置
1. 点击"确定"保存配置
2. 等待配置生效（通常1-5分钟）

## 🧪 验证配置

### 1. 检查域名验证文件
```bash
curl https://m.dboss.pro/MP_verify_xxxxxxxxxx.txt
```
应该返回文件内容，不是404错误。

### 2. 检查授权URL构建
在浏览器控制台中运行：
```javascript
// 检查当前页面信息
console.log('当前域名:', window.location.host)
console.log('当前协议:', window.location.protocol)
console.log('当前路径:', window.location.pathname)

// 检查构建的redirect_uri
const protocol = window.location.protocol
const host = window.location.host
const pathname = window.location.pathname
const redirectUri = `${protocol}//${host}${pathname}`
console.log('构建的redirect_uri:', redirectUri)
```

### 3. 使用调试工具
访问测试页面进行诊断：
```
https://m.dboss.pro/test
```
点击"诊断微信配置"按钮，查看控制台输出。

## 📋 配置检查清单

### 微信公众号后台
- [ ] 已登录微信公众平台
- [ ] 已进入"功能设置" → "网页授权域名"
- [ ] 已添加域名：`m.dboss.pro`（不包含协议和路径）
- [ ] 已下载验证文件
- [ ] 已上传验证文件到域名根目录
- [ ] 验证文件可以正常访问
- [ ] 已保存配置并等待生效

### 服务器配置
- [ ] wechat-h5项目已部署到 `https://m.dboss.pro`
- [ ] 域名解析正确
- [ ] HTTPS证书有效
- [ ] 验证文件在根目录可访问
- [ ] 页面可以正常打开

### 代码配置
- [ ] VUE_APP_WECHAT_APPID 配置正确
- [ ] redirect_uri 构建逻辑正确
- [ ] 参数传递逻辑正确

## 🔄 测试流程

### 1. 基础测试
```bash
# 测试域名访问
curl -I https://m.dboss.pro/

# 测试验证文件
curl https://m.dboss.pro/MP_verify_xxxxxxxxxx.txt

# 测试绑定页面
curl -I https://m.dboss.pro/store-wechat-bind
```

### 2. 微信环境测试
1. 在微信中访问：`https://m.dboss.pro/test`
2. 点击"诊断微信配置"查看配置状态
3. 点击"测试微信授权"查看授权URL构建
4. 访问绑定页面测试完整流程

### 3. 完整流程测试
1. 商家端生成二维码
2. 微信扫描二维码
3. 跳转到绑定确认页面
4. 点击"确认绑定"
5. 检查是否还有10003错误

## 🚨 常见问题

### Q1: 配置后仍然报10003错误
**解决方案**:
1. 检查域名格式是否正确（不包含协议和路径）
2. 等待配置生效（最多10分钟）
3. 清除微信缓存重试
4. 检查AppId是否正确

### Q2: 验证文件无法访问
**解决方案**:
1. 确认文件已上传到正确位置
2. 检查文件权限
3. 检查服务器配置
4. 确认域名解析正确

### Q3: 页面空白或404
**解决方案**:
1. 确认wechat-h5项目已正确部署
2. 检查Nginx配置支持history模式
3. 检查静态资源路径

### Q4: 其他错误码
参考微信官方文档：
- 10004: 公众号被封禁
- 10005: 没有网页授权权限
- 10006: 需要关注测试号
- 10009: 操作太频繁

## 📞 技术支持

如果按照以上步骤仍无法解决问题：

1. **检查日志**: 查看浏览器控制台和服务器日志
2. **使用调试工具**: 运行 `https://m.dboss.pro/test` 进行诊断
3. **联系微信客服**: 如果确认配置无误但仍有问题
4. **查看官方文档**: [微信网页授权文档](https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/Wechat_webpage_authorization.html)

## 📈 监控建议

建议添加以下监控：
1. 微信授权成功率监控
2. 错误码统计
3. 用户流失点分析
4. 域名访问监控
