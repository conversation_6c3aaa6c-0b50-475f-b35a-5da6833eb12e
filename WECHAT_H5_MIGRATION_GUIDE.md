# 微信H5项目迁移指南

## 迁移概述

本文档说明了如何将微信绑定确认功能从 `dbosshop-ui` 项目中剥离，并迁移到独立的 `wechat-h5` 项目中。

## 已迁移的文件

### 1. 从 seller 项目迁移的文件

#### 页面组件
- `seller/src/views/shop/wechatBindConfirm.vue` → `wechat-h5/src/views/BindConfirm.vue`

#### API接口
- `seller/src/api/shops.js` 中的微信相关接口 → `wechat-h5/src/api/wechat.js`
  - `confirmWechatBind`
  - `getWechatUserInfoByCode`

#### 文档
- `seller/src/views/shop/WECHAT_USER_INFO_README.md` → 已更新到新项目文档中

### 2. 新增的H5项目文件

```
wechat-h5/
├── public/
│   ├── index.html              # HTML模板 (微信优化)
│   ├── manifest.json           # PWA配置
│   └── favicon.ico             # 网站图标
├── src/
│   ├── api/
│   │   └── wechat.js           # 微信API接口
│   ├── components/
│   │   ├── PageLoading.vue     # 页面加载组件
│   │   └── EmptyState.vue      # 空状态组件
│   ├── router/
│   │   └── index.js            # 路由配置
│   ├── store/
│   │   └── index.js            # 状态管理
│   ├── styles/
│   │   └── index.less          # 全局样式
│   ├── utils/
│   │   ├── index.js            # 通用工具
│   │   ├── request.js          # HTTP请求封装
│   │   └── wechat.js           # 微信工具函数
│   ├── views/
│   │   ├── BindConfirm.vue     # 绑定确认页面
│   │   └── Error.vue           # 错误页面
│   ├── App.vue                 # 根组件
│   └── main.js                 # 入口文件
├── .env.development            # 开发环境配置
├── .env.production             # 生产环境配置
├── babel.config.js             # Babel配置
├── package.json                # 项目依赖
├── vue.config.js               # Vue CLI配置
└── README.md                   # 项目说明
```

## 需要清理的原项目文件

### 1. seller 项目中需要删除的文件

- `seller/src/views/shop/wechatBindConfirm.vue`
- `seller/src/views/shop/WECHAT_USER_INFO_README.md`

### 2. seller 项目中需要修改的文件

#### `seller/src/api/shops.js`
需要移除以下微信绑定确认相关的API方法：
- `getWechatUserInfoByCode` (仅用于绑定确认的方法)

保留以下方法（商家端管理功能仍需要）：
- `confirmWechatBind`
- `generateWechatOfficialAccountQRCode`
- `generateWechatMiniProgramQRCode`
- `checkWechatBindStatus`
- `getWechatBindStatus`
- `updateWechatNotification`
- `unbindWechat`

#### 路由配置
如果有配置 `wechatBindConfirm` 页面的路由，需要移除。

## 部署配置

### 1. H5项目部署

H5项目应该部署到独立的域名或子路径下，例如：
- `https://wechat.dboss.pro/`
- `https://www.dboss.pro/wechat/`

### 2. 后端接口调整

确保后端接口支持跨域访问，或者配置代理：

```nginx
# Nginx配置示例
server {
    listen 80;
    server_name wechat.dboss.pro;
    
    location / {
        root /var/www/wechat-h5/dist;
        try_files $uri $uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass https://store-api.dboss.pro;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. 微信公众号配置

更新微信公众号后台的授权回调域名：
- 原域名：`seller.dboss.pro`
- 新域名：`wechat.dboss.pro`

## 迁移后的优势

### 1. 技术优势
- **移动端优化**: 使用Vant UI组件库，专为移动端设计
- **性能优化**: 独立部署，减少主项目体积
- **维护性**: 代码分离，便于独立维护和更新
- **扩展性**: 可以方便地添加更多微信相关功能

### 2. 用户体验优势
- **加载速度**: 更小的包体积，更快的加载速度
- **移动适配**: 更好的移动端交互体验
- **微信集成**: 更深度的微信浏览器集成

### 3. 开发优势
- **独立开发**: 可以独立开发和部署
- **技术栈**: 可以使用更适合移动端的技术栈
- **测试**: 更容易进行移动端测试

## 注意事项

1. **域名配置**: 确保新域名已在微信公众号后台配置
2. **HTTPS**: 生产环境必须使用HTTPS
3. **兼容性**: 确保在各种微信版本中都能正常工作
4. **监控**: 添加错误监控和性能监控
5. **备份**: 在清理原项目代码前，确保已完成迁移和测试

## 测试清单

- [ ] H5项目可以正常构建和运行
- [ ] 微信绑定确认功能正常工作
- [ ] 微信网页授权流程正常
- [ ] 错误处理和用户提示正常
- [ ] 移动端适配效果良好
- [ ] 在不同微信版本中测试通过
- [ ] 后端接口调用正常
- [ ] 生产环境部署成功
