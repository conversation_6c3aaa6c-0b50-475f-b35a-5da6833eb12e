{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-f3f46918\"],{\"78c1\":function(t,o,e){\"use strict\";e.r(o);var n=function(){var t=this,o=t._self._c;return o(\"div\",{staticClass:\"test-page\"},[o(\"van-nav-bar\",{attrs:{title:\"测试页面\"}}),o(\"div\",{staticClass:\"test-content\"},[o(\"van-cell-group\",[o(\"van-cell\",{attrs:{title:\"项目状态\",value:\"正常运行\"}}),o(\"van-cell\",{attrs:{title:\"当前时间\",value:t.currentTime}}),o(\"van-cell\",{attrs:{title:\"当前URL\",value:t.currentUrl}}),o(\"van-cell\",{attrs:{title:\"用户代理\",value:t.userAgent}}),o(\"van-cell\",{attrs:{title:\"是否微信环境\",value:t.isWechat?\"是\":\"否\"}})],1),o(\"div\",{staticClass:\"test-buttons\"},[o(\"van-button\",{attrs:{type:\"primary\",block:\"\"},on:{click:t.testApi}},[t._v(\"测试API\")]),o(\"van-button\",{attrs:{type:\"success\",block:\"\"},on:{click:t.testNavigation}},[t._v(\"测试导航\")]),o(\"van-button\",{attrs:{type:\"warning\",block:\"\"},on:{click:t.testError}},[t._v(\"测试错误页面\")]),o(\"van-button\",{attrs:{type:\"info\",block:\"\"},on:{click:t.testWechatAuth}},[t._v(\"测试微信授权\")]),o(\"van-button\",{attrs:{type:\"default\",block:\"\"},on:{click:t.diagnoseWechat}},[t._v(\"诊断微信配置\")])],1),t.urlParams?o(\"div\",{staticClass:\"url-params\"},[o(\"van-divider\",[t._v(\"URL参数\")]),o(\"van-cell-group\",t._l(t.urlParams,(function(t,e){return o(\"van-cell\",{key:e,attrs:{title:e,value:t}})})),1)],1):t._e()],1)],1)},r=[],c=(e(\"14d9\"),e(\"ac1f\"),e(\"00b4\"),e(\"ed08\"));e(\"d401\"),e(\"99af\"),e(\"e9f5\"),e(\"7d54\"),e(\"d3b7\"),e(\"25f0\"),e(\"3ca3\"),e(\"841c\"),e(\"159b\"),e(\"ddb0\"),e(\"9861\"),e(\"88a7\"),e(\"271a\"),e(\"5494\");function a(){var t={appId:\"wx0542386236e94324\",domain:window.location.host,protocol:window.location.protocol,currentUrl:window.location.href,userAgent:navigator.userAgent,isWechat:/micromessenger/i.test(navigator.userAgent)};return console.group(\"🔍 微信授权配置检查\"),console.log(\"AppId:\",t.appId),console.log(\"当前域名:\",t.domain),console.log(\"协议:\",t.protocol),console.log(\"完整URL:\",t.currentUrl),console.log(\"是否微信环境:\",t.isWechat),console.log(\"User Agent:\",t.userAgent),console.groupEnd(),t}function s(t,o){var e=window.location.protocol,n=window.location.host,r=window.location.pathname,c=\"\".concat(e,\"//\").concat(n).concat(r),a=encodeURIComponent(c),s=Math.random().toString(36).substring(2,15),i=\"wx0542386236e94324\",l=\"snsapi_userinfo\",u=\"https://open.weixin.qq.com/connect/oauth2/authorize?appid=\".concat(i,\"&redirect_uri=\").concat(a,\"&response_type=code&scope=\").concat(l,\"&state=\").concat(s,\"#wechat_redirect\");return console.group(\"🔗 微信授权URL构建\"),console.log(\"AppId:\",i),console.log(\"回调地址:\",c),console.log(\"编码后回调地址:\",a),console.log(\"Scope:\",l),console.log(\"State:\",s),console.log(\"完整授权URL:\",u),console.groupEnd(),{appId:i,redirectUri:c,encodedRedirectUri:a,scope:l,state:s,authUrl:u}}function i(){var t=window.location.host,o=window.location.protocol,e=[\"📋 微信公众号后台配置检查清单:\",\"\",\"1. 登录微信公众平台 (mp.weixin.qq.com)\",'2. 进入\"设置与开发\" → \"公众号设置\" → \"功能设置\"','3. 在\"网页授权域名\"中添加以下域名:',\"   \".concat(t),\"\",\"⚠️ 注意事项:\",\"- 域名不要包含协议 (http:// 或 https://)\",\"- 域名不要包含路径\",\"- 确保域名已备案且可正常访问\",\"- 需要上传验证文件到域名根目录\",\"\",\"4. 验证文件配置:\",\"   下载验证文件并上传到: \".concat(o,\"//\").concat(t,\"/MP_verify_xxx.txt\"),\"\",\"5. 保存配置并等待生效 (通常几分钟内生效)\"];return console.group(\"⚙️ 微信公众号后台配置指南\"),e.forEach((function(t){return console.log(t)})),console.groupEnd(),{domain:t,protocol:o,verifyFileUrl:\"\".concat(o,\"//\").concat(t,\"/MP_verify_xxx.txt\"),configUrl:\"https://mp.weixin.qq.com\"}}function l(){var t=new URLSearchParams(window.location.search),o=t.get(\"code\"),e=t.get(\"state\"),n=t.get(\"error\"),r=t.get(\"error_description\"),c={code:o,state:e,error:n,errorDescription:r,hasCode:!!o,hasError:!!n,isCallback:!(!o&&!n)};return c.isCallback&&(console.group(\"📥 微信授权回调解析\"),console.log(\"Code:\",o),console.log(\"State:\",e),n&&(console.error(\"Error:\",n),console.error(\"Error Description:\",r)),console.log(\"回调结果:\",c),console.groupEnd()),c}function u(){console.group(\"🔧 微信授权问题诊断\");var t=a(),o=l();i();var e={timestamp:(new Date).toISOString(),config:t,callback:o,recommendations:[]};return t.appId&&\"YOUR_PROD_WECHAT_APPID\"!==t.appId||e.recommendations.push(\"⚠️ 请配置正确的微信公众号AppId\"),t.isWechat||e.recommendations.push(\"⚠️ 当前不在微信环境中，请在微信中测试\"),o.hasError&&(e.recommendations.push(\"❌ 授权失败: \".concat(o.error)),\"10003\"===o.error&&e.recommendations.push(\"🔧 请检查微信公众号后台的授权域名配置\")),\"http:\"===t.protocol&&\"localhost\"!==t.domain&&e.recommendations.push(\"⚠️ 生产环境建议使用HTTPS\"),console.log(\"📊 诊断报告:\",e),console.groupEnd(),e}var p={name:\"Test\",data:function(){return{currentTime:\"\",currentUrl:\"\",userAgent:\"\",isWechat:!1,urlParams:null}},mounted:function(){var t=this;this.initData(),this.updateTime(),setInterval((function(){t.updateTime()}),1e3)},methods:{initData:function(){this.currentUrl=window.location.href,this.userAgent=navigator.userAgent,this.isWechat=/micromessenger/i.test(navigator.userAgent),this.urlParams=Object(c[\"getUrlParams\"])()},updateTime:function(){this.currentTime=(new Date).toLocaleString()},testApi:function(){this.$toast(\"API测试功能待实现\")},testNavigation:function(){this.$router.push(\"/bind-confirm?token=test&type=WECHAT_OFFIACCOUNT_OPEN_ID\")},testError:function(){this.$router.push(\"/error?type=test&message=这是一个测试错误\")},testWechatAuth:function(){s(\"test-token\",\"WECHAT_OFFIACCOUNT_OPEN_ID\");this.$toast(\"请查看控制台输出的授权信息\")},diagnoseWechat:function(){var t=this,o=u();this.$toast(\"诊断完成，请查看控制台\"),o.recommendations.length>0&&setTimeout((function(){t.$dialog.alert({title:\"诊断结果\",message:o.recommendations.join(\"\\n\")})}),500)}}},d=p,g=(e(\"d106\"),e(\"0c7c\")),h=Object(g[\"a\"])(d,n,r,!1,null,\"5db9d000\",null);o[\"default\"]=h.exports},d106:function(t,o,e){\"use strict\";e(\"f326\")},f326:function(t,o,e){}}]);", "extractedComments": []}