{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-03ebdfb2\"],{\"0cb2\":function(r,e,t){\"use strict\";var n=t(\"e330\"),a=t(\"7b0b\"),s=Math.floor,o=n(\"\".charAt),i=n(\"\".replace),c=n(\"\".slice),u=/\\$([$&'`]|\\d{1,2}|<[^>]*>)/g,l=/\\$([$&'`]|\\d{1,2})/g;r.exports=function(r,e,t,n,f,d){var h=t+r.length,v=n.length,p=l;return void 0!==f&&(f=a(f),p=u),i(d,p,(function(a,i){var u;switch(o(i,0)){case\"$\":return\"$\";case\"&\":return r;case\"`\":return c(e,0,t);case\"'\":return c(e,h);case\"<\":u=f[c(i,1,-1)];break;default:var l=+i;if(0===l)return a;if(l>v){var d=s(l/10);return 0===d?a:d<=v?void 0===n[d-1]?o(i,1):n[d-1]+o(i,1):a}u=n[l-1]}return void 0===u?\"\":u}))}},5319:function(r,e,t){\"use strict\";var n=t(\"2ba4\"),a=t(\"c65b\"),s=t(\"e330\"),o=t(\"d784\"),i=t(\"d039\"),c=t(\"825a\"),u=t(\"1626\"),l=t(\"861d\"),f=t(\"5926\"),d=t(\"50c4\"),h=t(\"577e\"),v=t(\"1d80\"),p=t(\"8aa5\"),g=t(\"dc4a\"),w=t(\"0cb2\"),y=t(\"90d8\"),b=t(\"14c3\"),m=t(\"b622\"),$=m(\"replace\"),k=Math.max,C=Math.min,_=s([].concat),x=s([].push),T=s(\"\".indexOf),M=s(\"\".slice),I=function(r){return void 0===r?r:String(r)},R=function(){return\"$0\"===\"a\".replace(/./,\"$0\")}(),J=function(){return!!/./[$]&&\"\"===/./[$](\"a\",\"$0\")}(),z=!i((function(){var r=/./;return r.exec=function(){var r=[];return r.groups={a:\"7\"},r},\"7\"!==\"\".replace(r,\"$<a>\")}));o(\"replace\",(function(r,e,t){var s=J?\"$\":\"$0\";return[function(r,t){var n=v(this),s=l(r)?g(r,$):void 0;return s?a(s,r,n,t):a(e,h(n),r,t)},function(r,a){var o=c(this),i=h(r);if(\"string\"==typeof a&&-1===T(a,s)&&-1===T(a,\"$<\")){var l=t(e,o,i,a);if(l.done)return l.value}var v=u(a);v||(a=h(a));var g,m=h(y(o)),$=-1!==T(m,\"g\");$&&(g=-1!==T(m,\"u\"),o.lastIndex=0);var R,J=[];while(1){if(R=b(o,i),null===R)break;if(x(J,R),!$)break;var z=h(R[0]);\"\"===z&&(o.lastIndex=p(i,d(o.lastIndex),g))}for(var S=\"\",W=0,q=0;q<J.length;q++){R=J[q];for(var A,B=h(R[0]),O=k(C(f(R.index),i.length),0),j=[],E=1;E<R.length;E++)x(j,I(R[E]));var D=R.groups;if(v){var F=_([B],j,O,i);void 0!==D&&x(F,D),A=h(n(a,void 0,F))}else A=w(B,i,O,j,D,a);O>=W&&(S+=M(i,W,O)+A,W=O+B.length)}return S+M(i,W)}]}),!z||!R||J)},\"8aa5\":function(r,e,t){\"use strict\";var n=t(\"6547\").charAt;r.exports=function(r,e,t){return e+(t?n(r,e).length:1)}},a0d8:function(r,e,t){},ca39:function(r,e,t){\"use strict\";t(\"a0d8\")},dda8:function(r,e,t){\"use strict\";t.r(e);var n=function(){var r=this,e=r._self._c;return e(\"div\",{staticClass:\"error-page\"},[e(\"div\",{staticClass:\"error-container\"},[e(\"div\",{staticClass:\"error-icon\"},[e(\"van-icon\",{attrs:{name:r.errorIcon,size:\"80\",color:r.errorColor}})],1),e(\"div\",{staticClass:\"error-content\"},[e(\"h1\",{staticClass:\"error-title\"},[r._v(r._s(r.errorTitle))]),e(\"p\",{staticClass:\"error-message\"},[r._v(r._s(r.errorMessage))]),r.solutions.length>0?e(\"div\",{staticClass:\"solutions\"},[e(\"h3\",[r._v(\"解决方案：\")]),e(\"ul\",r._l(r.solutions,(function(t,n){return e(\"li\",{key:n},[r._v(\" \"+r._s(t)+\" \")])})),0)]):r._e()]),e(\"div\",{staticClass:\"error-actions\"},[r.showRetry?e(\"van-button\",{staticClass:\"action-button\",attrs:{type:\"primary\",size:\"large\"},on:{click:r.handleRetry}},[r._v(\" 重试 \")]):r._e(),e(\"van-button\",{staticClass:\"action-button\",attrs:{size:\"large\",type:r.showRetry?\"default\":\"primary\"},on:{click:r.handleClose}},[r._v(\" 关闭页面 \")])],1)])])},a=[],s=(t(\"caad\"),t(\"ac1f\"),t(\"5319\"),{name:\"Error\",data:function(){return{errorType:\"\",customMessage:\"\"}},computed:{errorIcon:function(){switch(this.errorType){case\"env\":return\"warning-o\";case\"network\":return\"wifi-o\";case\"auth\":return\"lock\";default:return\"close\"}},errorColor:function(){switch(this.errorType){case\"env\":return\"#ff976a\";case\"network\":return\"#1989fa\";case\"auth\":return\"#ee0a24\";default:return\"#ee0a24\"}},errorTitle:function(){switch(this.errorType){case\"env\":return\"环境不支持\";case\"network\":return\"网络错误\";case\"auth\":return\"授权失败\";default:return\"页面错误\"}},errorMessage:function(){if(this.customMessage)return this.customMessage;switch(this.errorType){case\"env\":return\"请在微信中打开此页面\";case\"network\":return\"网络连接失败，请检查网络设置\";case\"auth\":return\"授权验证失败，请重新授权\";default:return\"页面加载失败，请稍后重试\"}},solutions:function(){switch(this.errorType){case\"env\":return[\"使用微信扫描二维码打开页面\",\"在微信中点击链接访问\",\"确保在微信浏览器环境中使用\"];case\"network\":return[\"检查网络连接是否正常\",\"尝试切换网络环境\",\"稍后重新尝试\"];case\"auth\":return[\"重新扫描二维码\",\"确认微信授权权限\",\"联系客服获取帮助\"];default:return[\"刷新页面重试\",\"检查网络连接\",\"联系技术支持\"]}},showRetry:function(){return[\"network\",\"auth\"].includes(this.errorType)}},mounted:function(){this.errorType=this.$route.query.type||\"default\",this.customMessage=this.$route.query.message||\"\"},methods:{handleRetry:function(){this.$router.replace(\"/bind-confirm\")},handleClose:function(){var r=this;\"undefined\"!==typeof WeixinJSBridge?WeixinJSBridge.call(\"closeWindow\"):(window.close(),setTimeout((function(){r.$toast(\"请手动关闭此页面\")}),300))}}}),o=s,i=(t(\"ca39\"),t(\"0c7c\")),c=Object(i[\"a\"])(o,n,a,!1,null,\"bfbf6438\",null);e[\"default\"]=c.exports}}]);", "extractedComments": []}