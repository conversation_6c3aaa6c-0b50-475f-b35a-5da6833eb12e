{"code": "(window[\"webpackJsonp\"]=window[\"webpackJsonp\"]||[]).push([[\"chunk-0434cce4\"],{\"31bb\":function(e,t,n){},\"7f4d\":function(e,t,n){\"use strict\";n.r(t);n(\"f4b3\"),n(\"e9c4\");var a=function(){var e=this,t=e._self._c;return t(\"div\",{staticClass:\"bind-confirm-page\"},[t(\"van-nav-bar\",{attrs:{title:\"微信绑定确认\",\"left-arrow\":\"\",border:!1,fixed:!0,placeholder:!0},on:{\"click-left\":e.handleBack}}),t(\"div\",{staticClass:\"page-container\"},[t(\"div\",{staticClass:\"logo-section\"},[t(\"van-icon\",{attrs:{name:\"wechat\",size:\"60\",color:\"#07c160\"}}),t(\"h1\",{staticClass:\"title\"},[e._v(\"微信通知绑定确认\")])],1),e.bindStatus.loading?t(\"div\",{staticClass:\"loading-section\"},[t(\"van-loading\",{attrs:{size:\"24px\",vertical:\"\"}},[e._v(\"正在处理，请稍候...\")])],1):e.bindStatus.error?t(\"div\",{staticClass:\"error-section\"},[t(\"van-icon\",{attrs:{name:\"close\",size:\"60\",color:\"#ee0a24\"}}),t(\"h2\",{staticClass:\"error-title\"},[e._v(e._s(e.bindStatus.errorMessage))]),t(\"p\",{staticClass:\"error-detail\"},[e._v(e._s(e.bindStatus.errorDetail))]),t(\"van-button\",{staticClass:\"action-button\",attrs:{type:\"primary\",size:\"large\"},on:{click:e.handleClose}},[e._v(\" 关闭页面 \")])],1):e.bindStatus.confirmed?t(\"div\",{staticClass:\"success-section\"},[t(\"van-icon\",{attrs:{name:\"checked\",size:\"60\",color:\"#07c160\"}}),t(\"h2\",{staticClass:\"success-title\"},[e._v(\"绑定成功\")]),t(\"p\",{staticClass:\"success-detail\"},[e._v(\"您已成功绑定\"+e._s(e.bindTypeText))]),t(\"van-button\",{staticClass:\"action-button\",attrs:{type:\"primary\",size:\"large\"},on:{click:e.handleClose}},[e._v(\" 关闭页面 \")])],1):t(\"div\",{staticClass:\"confirm-section\"},[t(\"div\",{staticClass:\"bind-info\"},[t(\"van-cell-group\",{staticClass:\"info-card\",attrs:{border:!1}},[t(\"van-cell\",{attrs:{title:e.bindTypeText,label:e.bindDescription,icon:\"info-o\"}})],1)],1),t(\"div\",{staticClass:\"action-buttons\"},[t(\"van-button\",{staticClass:\"cancel-button\",attrs:{size:\"large\"},on:{click:e.handleCancel}},[e._v(\" 取消 \")]),t(\"van-button\",{staticClass:\"confirm-button\",attrs:{type:\"primary\",size:\"large\",loading:e.confirming},on:{click:e.handleConfirm}},[e._v(\" 确认绑定 \")])],1)]),e.isDev&&e.result?t(\"div\",{staticClass:\"debug-section\"},[t(\"van-divider\",[e._v(\"调试信息\")]),t(\"pre\",{staticClass:\"debug-info\"},[e._v(e._s(JSON.stringify(e.result,null,2)))])],1):e._e()])],1)},r=[],s=n(\"c14f\"),i=n(\"1da1\"),c=n(\"5530\"),o=(n(\"d9e2\"),n(\"d401\"),n(\"99af\"),n(\"caad\"),n(\"2f62\")),u=n(\"53ca\"),l=(n(\"e7e5\"),n(\"d399\")),d=(n(\"d3b7\"),n(\"bc3a\")),b=n.n(d),f=b.a.create({baseURL:\"https://store-api.dboss.pro\",timeout:1e4,headers:{\"Content-Type\":\"application/json;charset=UTF-8\"}});f.interceptors.request.use((function(e){return\"get\"===e.method&&(e.params=Object(c[\"a\"])(Object(c[\"a\"])({},e.params),{},{_t:Date.now()})),!1!==e.showLoading&&l[\"a\"].loading({message:\"加载中...\",forbidClick:!0,duration:0}),e}),(function(e){return l[\"a\"].clear(),l[\"a\"].fail(\"请求配置错误\"),Promise.reject(e)})),f.interceptors.response.use((function(e){l[\"a\"].clear();var t=e.data;if(t&&\"object\"===Object(u[\"a\"])(t)){if(!0===t.success)return t;var n=t.message||\"请求失败\";return l[\"a\"].fail(n),Promise.reject(new Error(n))}return t}),(function(e){l[\"a\"].clear();var t=\"网络错误，请稍后重试\";if(e.response){var n=e.response,a=n.status,r=n.data;switch(a){case 400:t=(null===r||void 0===r?void 0:r.message)||\"请求参数错误\";break;case 401:t=\"未授权，请重新登录\";break;case 403:t=\"拒绝访问\";break;case 404:t=\"请求的资源不存在\";break;case 500:t=\"服务器内部错误\";break;default:t=(null===r||void 0===r?void 0:r.message)||\"请求失败 (\".concat(a,\")\")}}else\"ECONNABORTED\"===e.code&&(t=\"请求超时，请稍后重试\");return l[\"a\"].fail(t),Promise.reject(e)}));var v=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return f(Object(c[\"a\"])({method:\"post\",url:e,data:t},n))},p=function(e){return v(\"/store/wechat-bind/getUserInfo\",{code:e})},h=function(e){return v(\"/store/wechat-bind/confirm\",e)},g=n(\"ed08\"),m={name:\"BindConfirm\",data:function(){return{token:\"\",unionType:\"\",confirming:!1,result:null,isDev:!1}},computed:Object(c[\"a\"])(Object(c[\"a\"])({},Object(o[\"c\"])([\"bindStatus\",\"isWechat\"])),{},{bindTypeText:function(){return\"WECHAT_OFFIACCOUNT_OPEN_ID\"===this.unionType?\"微信公众号\":\"微信小程序\"},bindDescription:function(){return\"绑定后，您将可以接收订单付款等重要通知\"}}),mounted:function(){this.initPage()},methods:Object(c[\"a\"])(Object(c[\"a\"])({},Object(o[\"b\"])([\"setBindStatus\",\"resetBindStatus\"])),{},{initPage:function(){var e=this;return Object(i[\"a\"])(Object(s[\"a\"])().m((function t(){var n,a;return Object(s[\"a\"])().w((function(t){while(1)switch(t.n){case 0:if(e.setBindStatus({loading:!0}),t.p=1,n=Object(g[\"getUrlParams\"])(),e.token=n.token||\"\",e.unionType=n.type||\"\",e.token&&e.unionType){t.n=2;break}throw new Error(\"缺少必要的绑定参数，请重新扫描二维码\");case 2:if([\"WECHAT_OFFIACCOUNT_OPEN_ID\",\"WECHAT_MP_OPEN_ID\"].includes(e.unionType)){t.n=3;break}throw new Error(\"绑定类型不正确，请重新扫描二维码\");case 3:e.setBindStatus({loading:!1}),t.n=5;break;case 4:t.p=4,a=t.v,e.setBindStatus({loading:!1,error:!0,errorMessage:\"参数错误\",errorDetail:a.message});case 5:return t.a(2)}}),t,null,[[1,4]])})))()},getWechatUserInfo:function(){var e=this;return Object(i[\"a\"])(Object(s[\"a\"])().m((function t(){var n,a;return Object(s[\"a\"])().w((function(t){while(1)switch(t.n){case 0:if(n=Object(g[\"getUrlParams\"])(),a=n.code,!a){t.n=2;break}return t.n=1,e.getUserInfoByCode(a);case 1:return t.a(2,t.v);case 2:throw e.redirectToWechatAuth(),new Error(\"正在跳转到微信授权页面...\");case 3:return t.a(2)}}),t)})))()},redirectToWechatAuth:function(){var e=window.location.href.split(\"?\")[0],t=encodeURIComponent(e+\"?token=\".concat(this.token,\"&type=\").concat(this.unionType)),n=Object(g[\"generateRandomString\"])(),a=\"wx0542386236e94324\",r=\"snsapi_userinfo\",s=\"https://open.weixin.qq.com/connect/oauth2/authorize?appid=\".concat(a,\"&redirect_uri=\").concat(t,\"&response_type=code&scope=\").concat(r,\"&state=\").concat(n,\"#wechat_redirect\");sessionStorage.setItem(\"wechat_auth_state\",n),window.location.href=s},getUserInfoByCode:function(e){return Object(i[\"a\"])(Object(s[\"a\"])().m((function t(){var n,a,r,i,c,o;return Object(s[\"a\"])().w((function(t){while(1)switch(t.n){case 0:if(t.p=0,n=Object(g[\"getUrlParams\"])(),a=n.state,r=sessionStorage.getItem(\"wechat_auth_state\"),!a||!r||a===r){t.n=1;break}throw new Error(\"授权验证失败，请重新授权\");case 1:return t.n=2,p(e);case 2:if(i=t.v,!i.success){t.n=3;break}return c=i.result,Object(g[\"cleanUrlParams\"])([\"code\",\"state\"]),sessionStorage.removeItem(\"wechat_auth_state\"),t.a(2,{unionId:c.unionid||\"\",openId:c.openid||\"\",nickname:c.nickname||\"微信用户\",avatar:c.headimgurl||\"\",gender:c.sex||0,city:c.city||\"\",province:c.province||\"\",country:c.country||\"\",code:e});case 3:throw new Error(i.message||\"获取用户信息失败\");case 4:t.n=6;break;case 5:throw t.p=5,o=t.v,console.error(\"通过code获取用户信息失败:\",o),o;case 6:return t.a(2)}}),t,null,[[0,5]])})))()},handleConfirm:function(){var e=this;return Object(i[\"a\"])(Object(s[\"a\"])().m((function t(){var n,a,r;return Object(s[\"a\"])().w((function(t){while(1)switch(t.n){case 0:return e.confirming=!0,t.p=1,t.n=2,e.getWechatUserInfo();case 2:return n=t.v,t.n=3,h({bindToken:e.token,unionType:e.unionType,unionId:n.unionId,openId:n.openId,code:n.code,nickname:n.nickname,avatar:n.avatar,gender:n.gender,city:n.city,province:n.province,country:n.country});case 3:a=t.v,e.result=a,a.success?e.setBindStatus({confirmed:!0}):e.setBindStatus({error:!0,errorMessage:\"绑定失败\",errorDetail:a.message||\"请重新扫描二维码\"}),t.n=5;break;case 4:t.p=4,r=t.v,console.error(\"确认绑定失败:\",r),e.setBindStatus({error:!0,errorMessage:\"绑定失败\",errorDetail:r.message||\"网络错误或服务器异常，请重试\"});case 5:return t.p=5,e.confirming=!1,t.f(5);case 6:return t.a(2)}}),t,null,[[1,4,5,6]])})))()},handleCancel:function(){this.setBindStatus({error:!0,errorMessage:\"已取消绑定\",errorDetail:\"您已取消绑定操作\"})},handleBack:function(){this.handleClose()},handleClose:function(){var e=this;\"undefined\"!==typeof WeixinJSBridge?WeixinJSBridge.call(\"closeWindow\"):(window.close(),setTimeout((function(){e.$toast(\"请手动关闭此页面\")}),300))}})},w=m,C=(n(\"914b\"),n(\"0c7c\")),_=Object(C[\"a\"])(w,a,r,!1,null,\"2f215002\",null);t[\"default\"]=_.exports},\"914b\":function(e,t,n){\"use strict\";n(\"31bb\")}}]);", "extractedComments": []}