{"_from": "glob-parent@^3.1.0", "_id": "glob-parent@3.1.0", "_inBundle": false, "_integrity": "sha512-E8Ak/2+dZY6fnzlR7+ueWvhsH1SjHr4jjss4YS/h4py44jY9MhK/VFdaZJAWDz6BbL21KeteKxFSFpq8OS5gVA==", "_location": "/webpack-dev-server/glob-parent", "_phantomChildren": {"is-extglob": "2.1.1"}, "_requested": {"type": "range", "registry": true, "raw": "glob-parent@^3.1.0", "name": "glob-parent", "escapedName": "glob-parent", "rawSpec": "^3.1.0", "saveSpec": null, "fetchSpec": "^3.1.0"}, "_requiredBy": ["/webpack-dev-server/chokidar"], "_resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-3.1.0.tgz", "_shasum": "9e6af6299d8d3bd2bd40430832bd113df906c5ae", "_spec": "glob-parent@^3.1.0", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/webpack-dev-server/node_modules/chokidar", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, "bugs": {"url": "https://github.com/es128/glob-parent/issues"}, "bundleDependencies": false, "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "deprecated": false, "description": "Strips glob magic from a string to provide the parent directory path", "devDependencies": {"coveralls": "^2.11.2", "istanbul": "^0.3.5", "mocha": "^2.1.0"}, "files": ["index.js"], "homepage": "https://github.com/es128/glob-parent", "keywords": ["glob", "parent", "strip", "path", "dirname", "directory", "base", "wildcard"], "license": "ISC", "main": "index.js", "name": "glob-parent", "repository": {"type": "git", "url": "git+https://github.com/es128/glob-parent.git"}, "scripts": {"ci-test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls", "test": "istanbul test node_modules/mocha/bin/_mocha"}, "version": "3.1.0"}