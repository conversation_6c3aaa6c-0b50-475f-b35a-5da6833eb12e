{"_from": "is-glob@^3.1.0", "_id": "is-glob@3.1.0", "_inBundle": false, "_integrity": "sha512-UFpDDrPgM6qpnFNI+rh/p3bUaq9hKLZN8bMUWzxmcnZVS3omf4IPK+BrewlnWjO1WmUsMYuSjKh4UJuV4+Lqmw==", "_location": "/webpack-dev-server/glob-parent/is-glob", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-glob@^3.1.0", "name": "is-glob", "escapedName": "is-glob", "rawSpec": "^3.1.0", "saveSpec": null, "fetchSpec": "^3.1.0"}, "_requiredBy": ["/webpack-dev-server/glob-parent"], "_resolved": "https://registry.npmjs.org/is-glob/-/is-glob-3.1.0.tgz", "_shasum": "7ba5ae24217804ac70707b96922567486cc3e84a", "_spec": "is-glob@^3.1.0", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/webpack-dev-server/node_modules/glob-parent", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/is-glob/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tuvistavie.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://twitter.com/jonschlinkert"}], "dependencies": {"is-extglob": "^2.1.0"}, "deprecated": false, "description": "Returns `true` if the given string looks like a glob pattern or an extglob pattern. This makes it easy to create code that only uses external modules like node-glob when necessary, resulting in much faster code execution and initialization time, and a better user experience.", "devDependencies": {"gulp-format-md": "^0.1.10", "mocha": "^3.0.2"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/is-glob", "keywords": ["bash", "braces", "check", "exec", "expression", "extglob", "glob", "globbing", "globstar", "is", "match", "matches", "pattern", "regex", "regular", "string", "test"], "license": "MIT", "main": "index.js", "name": "is-glob", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-glob.git"}, "scripts": {"test": "mocha"}, "verb": {"layout": "default", "plugins": ["gulp-format-md"], "related": {"list": ["assemble", "base", "update", "verb"]}, "reflinks": ["assemble", "bach", "base", "composer", "gulp", "has-glob", "is-valid-glob", "micromatch", "npm", "scaffold", "verb", "vinyl"]}, "version": "3.1.0"}