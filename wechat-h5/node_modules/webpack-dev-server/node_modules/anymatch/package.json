{"_from": "anymatch@^2.0.0", "_id": "anymatch@2.0.0", "_inBundle": false, "_integrity": "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==", "_location": "/webpack-dev-server/anymatch", "_phantomChildren": {"remove-trailing-separator": "1.1.0"}, "_requested": {"type": "range", "registry": true, "raw": "anymatch@^2.0.0", "name": "anymatch", "escapedName": "anymatch", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/webpack-dev-server/chokidar"], "_resolved": "https://registry.npmjs.org/anymatch/-/anymatch-2.0.0.tgz", "_shasum": "bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb", "_spec": "anymatch@^2.0.0", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/webpack-dev-server/node_modules/chokidar", "author": {"name": "<PERSON><PERSON>", "url": "http://github.com/es128"}, "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "bundleDependencies": false, "dependencies": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}, "deprecated": false, "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "devDependencies": {"coveralls": "^2.7.0", "istanbul": "^0.4.5", "mocha": "^3.0.0"}, "files": ["index.js"], "homepage": "https://github.com/micromatch/anymatch", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "license": "ISC", "name": "anymatch", "repository": {"type": "git", "url": "git+https://github.com/micromatch/anymatch.git"}, "scripts": {"test": "istanbul cover _mocha && cat ./coverage/lcov.info | coveralls"}, "version": "2.0.0"}