{"_from": "strip-ansi@^5.1.0", "_id": "strip-ansi@5.2.0", "_inBundle": false, "_integrity": "sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==", "_location": "/webpack-dev-server/string-width/strip-ansi", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "strip-ansi@^5.1.0", "name": "strip-ansi", "escapedName": "strip-ansi", "rawSpec": "^5.1.0", "saveSpec": null, "fetchSpec": "^5.1.0"}, "_requiredBy": ["/webpack-dev-server/string-width"], "_resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz", "_shasum": "8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae", "_spec": "strip-ansi@^5.1.0", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/webpack-dev-server/node_modules/string-width", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/strip-ansi/issues"}, "bundleDependencies": false, "dependencies": {"ansi-regex": "^4.1.0"}, "deprecated": false, "description": "Strip ANSI escape codes from a string", "devDependencies": {"ava": "^1.3.1", "tsd-check": "^0.5.0", "xo": "^0.24.0"}, "engines": {"node": ">=6"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/chalk/strip-ansi#readme", "keywords": ["strip", "trim", "remove", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "name": "strip-ansi", "repository": {"type": "git", "url": "git+https://github.com/chalk/strip-ansi.git"}, "scripts": {"test": "xo && ava && tsd-check"}, "version": "5.2.0"}