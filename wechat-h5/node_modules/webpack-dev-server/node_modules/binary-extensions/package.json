{"_from": "binary-extensions@^1.0.0", "_id": "binary-extensions@1.13.1", "_inBundle": false, "_integrity": "sha512-Un7MIEDdUC5gNpcGDV97op1Ywk748MpHcFTHoYs6qnj1Z3j7I53VG3nwZhKzoBZmbdRNnb6WRdFlwl7tSDuZGw==", "_location": "/webpack-dev-server/binary-extensions", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "binary-extensions@^1.0.0", "name": "binary-extensions", "escapedName": "binary-extensions", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/webpack-dev-server/is-binary-path"], "_resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-1.13.1.tgz", "_shasum": "598afe54755b2868a5330d2aff9d4ebb53209b65", "_spec": "binary-extensions@^1.0.0", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/webpack-dev-server/node_modules/is-binary-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "bundleDependencies": false, "deprecated": false, "description": "List of binary file extensions", "devDependencies": {"ava": "0.16.0"}, "engines": {"node": ">=0.10.0"}, "files": ["binary-extensions.json"], "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "json", "list", "array"], "license": "MIT", "main": "binary-extensions.json", "name": "binary-extensions", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/binary-extensions.git"}, "scripts": {"test": "ava"}, "version": "1.13.1"}