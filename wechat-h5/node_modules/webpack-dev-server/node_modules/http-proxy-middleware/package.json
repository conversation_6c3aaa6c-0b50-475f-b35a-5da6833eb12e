{"_from": "http-proxy-middleware@0.19.1", "_id": "http-proxy-middleware@0.19.1", "_inBundle": false, "_integrity": "sha512-yHYTgWMQO8VvwNS22eLLloAkvungsKdKTLO8AJlftYIKNfJr3GK3zK0ZCfzDDGUBttdGc8xFy1mCitvNKQtC3Q==", "_location": "/webpack-dev-server/http-proxy-middleware", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "http-proxy-middleware@0.19.1", "name": "http-proxy-middleware", "escapedName": "http-proxy-middleware", "rawSpec": "0.19.1", "saveSpec": null, "fetchSpec": "0.19.1"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "https://registry.npmjs.org/http-proxy-middleware/-/http-proxy-middleware-0.19.1.tgz", "_shasum": "183c7dc4aa1479150306498c210cdaf96080a43a", "_spec": "http-proxy-middleware@0.19.1", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/webpack-dev-server", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/chimurai/http-proxy-middleware/issues"}, "bundleDependencies": false, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "dependencies": {"http-proxy": "^1.17.0", "is-glob": "^4.0.0", "lodash": "^4.17.11", "micromatch": "^3.1.10"}, "deprecated": false, "description": "The one-liner node.js proxy middleware for connect, express and browser-sync", "devDependencies": {"@commitlint/cli": "^7.2.1", "@commitlint/config-conventional": "^7.1.2", "browser-sync": "^2.26.3", "chai": "^4.2.0", "connect": "^3.6.6", "coveralls": "^3.0.2", "express": "^4.16.4", "husky": "^1.2.0", "istanbul": "^0.4.5", "istanbul-coveralls": "^1.0.3", "mocha": "^5.2.0", "mocha-lcov-reporter": "1.3.0", "opn": "^5.4.0", "precise-commits": "^1.0.2", "prettier": "^1.15.2", "ws": "^6.1.2"}, "engines": {"node": ">=4.0.0"}, "files": ["index.js", "lib"], "homepage": "https://github.com/chimurai/http-proxy-middleware", "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "precise-commits"}}, "keywords": ["reverse", "proxy", "middleware", "http", "https", "connect", "express", "browser-sync", "gulp", "grunt-contrib-connect", "websocket", "ws", "cors"], "license": "MIT", "main": "index.js", "name": "http-proxy-middleware", "repository": {"type": "git", "url": "git+https://github.com/chimurai/http-proxy-middleware.git"}, "scripts": {"clean": "rm -rf coverage", "cover": "npm run clean && istanbul cover ./node_modules/mocha/bin/_mocha -- --recursive", "coveralls": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly -- --recursive --reporter spec && istanbul-coveralls && npm run clean", "lint": "prettier \"**/*.{js,md}\" --list-different", "lint:fix": "prettier \"**/*.{js,md}\" --write", "test": "npm run lint && mocha --recursive --colors --reporter spec"}, "version": "0.19.1"}