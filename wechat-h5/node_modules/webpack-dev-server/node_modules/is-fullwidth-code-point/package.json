{"_from": "is-fullwidth-code-point@^2.0.0", "_id": "is-fullwidth-code-point@2.0.0", "_inBundle": false, "_integrity": "sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==", "_location": "/webpack-dev-server/is-fullwidth-code-point", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-fullwidth-code-point@^2.0.0", "name": "is-fullwidth-code-point", "escapedName": "is-fullwidth-code-point", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/webpack-dev-server/string-width"], "_resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "_shasum": "a3b30a5c4f199183167aaab93beefae3ddfb654f", "_spec": "is-fullwidth-code-point@^2.0.0", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/webpack-dev-server/node_modules/string-width", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-fullwidth-code-point/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if the character represented by a given Unicode code point is fullwidth", "devDependencies": {"ava": "*", "xo": "*"}, "engines": {"node": ">=4"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/is-fullwidth-code-point#readme", "keywords": ["fullwidth", "full-width", "full", "width", "unicode", "character", "char", "string", "str", "codepoint", "code", "point", "is", "detect", "check"], "license": "MIT", "name": "is-fullwidth-code-point", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-fullwidth-code-point.git"}, "scripts": {"test": "xo && ava"}, "version": "2.0.0", "xo": {"esnext": true}}