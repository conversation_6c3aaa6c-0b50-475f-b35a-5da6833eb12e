{"_from": "emoji-regex@^7.0.1", "_id": "emoji-regex@7.0.3", "_inBundle": false, "_integrity": "sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==", "_location": "/webpack-dev-server/emoji-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "emoji-regex@^7.0.1", "name": "emoji-regex", "escapedName": "emoji-regex", "rawSpec": "^7.0.1", "saveSpec": null, "fetchSpec": "^7.0.1"}, "_requiredBy": ["/webpack-dev-server/string-width"], "_resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-7.0.3.tgz", "_shasum": "933a04052860c85e83c122479c4748a8e4c72156", "_spec": "emoji-regex@^7.0.1", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/webpack-dev-server/node_modules/string-width", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.0.0", "@babel/preset-env": "^7.0.0", "mocha": "^5.2.0", "regexgen": "^1.3.0", "unicode-11.0.0": "^0.7.7", "unicode-tr51": "^9.0.1"}, "files": ["LICENSE-MIT.txt", "index.js", "index.d.ts", "text.js", "es2015/index.js", "es2015/text.js"], "homepage": "https://mths.be/emoji-regex", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "main": "index.js", "name": "emoji-regex", "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "types": "index.d.ts", "version": "7.0.3"}