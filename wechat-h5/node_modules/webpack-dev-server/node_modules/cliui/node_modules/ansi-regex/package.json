{"_from": "ansi-regex@^4.1.0", "_id": "ansi-regex@4.1.1", "_inBundle": false, "_integrity": "sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==", "_location": "/webpack-dev-server/cliui/ansi-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ansi-regex@^4.1.0", "name": "ansi-regex", "escapedName": "ansi-regex", "rawSpec": "^4.1.0", "saveSpec": null, "fetchSpec": "^4.1.0"}, "_requiredBy": ["/webpack-dev-server/cliui/strip-ansi"], "_resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.1.1.tgz", "_shasum": "164daac87ab2d6f6db3a29875e2d1766582dabed", "_spec": "ansi-regex@^4.1.0", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/webpack-dev-server/node_modules/cliui/node_modules/strip-ansi", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/ansi-regex/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Regular expression for matching ANSI escape codes", "devDependencies": {"ava": "^0.25.0", "xo": "^0.23.0"}, "engines": {"node": ">=6"}, "files": ["index.js"], "homepage": "https://github.com/chalk/ansi-regex#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "command-line", "text", "regex", "regexp", "re", "match", "test", "find", "pattern"], "license": "MIT", "name": "ansi-regex", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-regex.git"}, "scripts": {"test": "xo && ava", "view-supported": "node fixtures/view-codes.js"}, "version": "4.1.1"}