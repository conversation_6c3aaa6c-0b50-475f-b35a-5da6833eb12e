{"_from": "cliui@^5.0.0", "_id": "cliui@5.0.0", "_inBundle": false, "_integrity": "sha512-PYeGSEmmHM6zvoef2w8TPzlrnNpXIjTipYK780YswmIP9vjxmd6Y2a3CB2Ks6/AU8NHjZugXvo8w3oWM2qnwXA==", "_location": "/webpack-dev-server/cliui", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cliui@^5.0.0", "name": "cliui", "escapedName": "cliui", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["/webpack-dev-server/yargs"], "_resolved": "https://registry.npmjs.org/cliui/-/cliui-5.0.0.tgz", "_shasum": "deefcfdb2e800784aa34f46fa08e06851c7bbbc5", "_spec": "cliui@^5.0.0", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/webpack-dev-server/node_modules/yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": ["index.js"], "data-cover-never": ["node_modules", "test"], "output-reporter": "spec"}}, "dependencies": {"string-width": "^3.1.0", "strip-ansi": "^5.2.0", "wrap-ansi": "^5.1.0"}, "deprecated": false, "description": "easily create complex multi-column command-line-interfaces", "devDependencies": {"chai": "^4.2.0", "chalk": "^2.4.2", "coveralls": "^3.0.3", "mocha": "^6.0.2", "nyc": "^13.3.0", "standard": "^12.0.1", "standard-version": "^5.0.2"}, "engine": {"node": ">=6"}, "files": ["index.js"], "homepage": "https://github.com/yargs/cliui#readme", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "license": "ISC", "main": "index.js", "name": "cliui", "repository": {"type": "git", "url": "git+ssh://**************/yargs/cliui.git"}, "scripts": {"coverage": "nyc --reporter=text-lcov mocha | coveralls", "pretest": "standard", "release": "standard-version", "test": "nyc mocha"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "version": "5.0.0"}