{"_from": "is-absolute-url@^3.0.3", "_id": "is-absolute-url@3.0.3", "_inBundle": false, "_integrity": "sha512-opmNIX7uFnS96NtPmhWQgQx6/NYFgsUXYMllcfzwWKUMwfo8kku1TvE6hkNcH+Q1ts5cMVrsY7j0bxXQDciu9Q==", "_location": "/webpack-dev-server/is-absolute-url", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-absolute-url@^3.0.3", "name": "is-absolute-url", "escapedName": "is-absolute-url", "rawSpec": "^3.0.3", "saveSpec": null, "fetchSpec": "^3.0.3"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "https://registry.npmjs.org/is-absolute-url/-/is-absolute-url-3.0.3.tgz", "_shasum": "96c6a22b6a23929b11ea0afb1836c36ad4a5d698", "_spec": "is-absolute-url@^3.0.3", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/webpack-dev-server", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-absolute-url/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if a URL is absolute", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/is-absolute-url#readme", "keywords": ["url", "absolute", "relative", "uri", "is", "check"], "license": "MIT", "name": "is-absolute-url", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-absolute-url.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "3.0.3"}