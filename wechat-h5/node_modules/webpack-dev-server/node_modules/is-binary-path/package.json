{"_from": "is-binary-path@^1.0.0", "_id": "is-binary-path@1.0.1", "_inBundle": false, "_integrity": "sha512-9fRVlXc0uCxEDj1nQzaWONSpbTfx0FmJfzHF7pwlI8DkWGoHBBea4Pg5Ky0ojwwxQmnSifgbKkI06Qv0Ljgj+Q==", "_location": "/webpack-dev-server/is-binary-path", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "is-binary-path@^1.0.0", "name": "is-binary-path", "escapedName": "is-binary-path", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/webpack-dev-server/chokidar"], "_resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-1.0.1.tgz", "_shasum": "75f16642b480f187a711c814161fd3a4a7655898", "_spec": "is-binary-path@^1.0.0", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/webpack-dev-server/node_modules/chokidar", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/is-binary-path/issues"}, "bundleDependencies": false, "dependencies": {"binary-extensions": "^1.0.0"}, "deprecated": false, "description": "Check if a filepath is a binary file", "devDependencies": {"ava": "0.0.4"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/sindresorhus/is-binary-path#readme", "keywords": ["bin", "binary", "ext", "extensions", "extension", "file", "path", "check", "detect", "is"], "license": "MIT", "name": "is-binary-path", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/is-binary-path.git"}, "scripts": {"test": "node test.js"}, "version": "1.0.1"}