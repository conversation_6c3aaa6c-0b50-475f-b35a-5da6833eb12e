{"name": "vendors", "version": "1.0.4", "description": "List of vendor prefixes known to the web platform", "license": "MIT", "keywords": ["css", "html", "dom", "web", "platform", "vendor", "prefix", "prefixes"], "repository": "wooorm/vendors", "bugs": "https://github.com/wooorm/vendors/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "main": "index.json", "files": ["index.json"], "dependencies": {}, "devDependencies": {"browserify": "^16.0.0", "prettier": "^1.0.0", "remark-cli": "^7.0.0", "remark-preset-wooorm": "^6.0.0", "tape": "^4.0.0", "tinyify": "^2.0.0", "xo": "^0.25.0"}, "scripts": {"format": "remark . -qfo && prettier --write \"**/*.js\" && xo --fix", "build-bundle": "browserify index.json -s vendors -o vendors.js", "build-mangle": "browserify index.json -s vendors -p tinyify -o vendors.min.js", "build": "npm run build-bundle && npm run build-mangle", "test-api": "node test", "test": "npm run format && npm run build && npm run test-api"}, "prettier": {"tabWidth": 2, "useTabs": false, "singleQuote": true, "bracketSpacing": false, "semi": false, "trailingComma": "none"}, "xo": {"prettier": true, "esnext": false, "ignores": ["vendors.js"]}, "remarkConfig": {"plugins": ["preset-wooorm"]}}