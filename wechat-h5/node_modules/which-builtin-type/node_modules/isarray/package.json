{"_from": "isarray@^2.0.5", "_id": "isarray@2.0.5", "_inBundle": false, "_integrity": "sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==", "_location": "/which-builtin-type/isarray", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "isarray@^2.0.5", "name": "isarray", "escapedName": "isarray", "rawSpec": "^2.0.5", "saveSpec": null, "fetchSpec": "^2.0.5"}, "_requiredBy": ["/which-builtin-type"], "_resolved": "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz", "_shasum": "8af1e4c1221244cc62459faf38940d4e644a5723", "_spec": "isarray@^2.0.5", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/which-builtin-type", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "bugs": {"url": "https://github.com/juliangruber/isarray/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Array#isArray for older browsers", "devDependencies": {"tape": "~2.13.4"}, "files": ["index.js"], "homepage": "https://github.com/juliangruber/isarray", "keywords": ["browser", "isarray", "array"], "license": "MIT", "main": "index.js", "name": "isarray", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "scripts": {"test": "tape test.js"}, "testling": {"files": "test.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "2.0.5"}