{"_from": "cliui@^7.0.2", "_id": "cliui@7.0.4", "_inBundle": false, "_integrity": "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==", "_location": "/yargs/cliui", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cliui@^7.0.2", "name": "cliui", "escapedName": "cliui", "rawSpec": "^7.0.2", "saveSpec": null, "fetchSpec": "^7.0.2"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz", "_shasum": "a0265ee655476fc807aea9df3df8df7783808b4f", "_spec": "cliui@^7.0.2", "_where": "/root/dbosshop/dbosshop-ui/wechat-h5/node_modules/yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "bundleDependencies": false, "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}, "deprecated": false, "description": "easily create complex multi-column command-line-interfaces", "devDependencies": {"@types/node": "^14.0.27", "@typescript-eslint/eslint-plugin": "^4.0.0", "@typescript-eslint/parser": "^4.0.0", "@wessberg/rollup-plugin-ts": "^1.3.2", "c8": "^7.3.0", "chai": "^4.2.0", "chalk": "^4.1.0", "cross-env": "^7.0.2", "eslint": "^7.6.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-node": "^11.1.0", "gts": "^3.0.0", "mocha": "^8.1.1", "rimraf": "^3.0.2", "rollup": "^2.23.1", "standardx": "^7.0.0", "typescript": "^4.0.0"}, "engine": {"node": ">=10"}, "exports": {".": [{"import": "./index.mjs", "require": "./build/index.cjs"}, "./build/index.cjs"]}, "files": ["build", "index.mjs", "!*.d.ts"], "homepage": "https://github.com/yargs/cliui#readme", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "license": "ISC", "main": "build/index.cjs", "module": "./index.mjs", "name": "cliui", "repository": {"type": "git", "url": "git+https://github.com/yargs/cliui.git"}, "scripts": {"build:cjs": "rollup -c", "check": "standardx '**/*.ts' && standardx '**/*.js' && standardx '**/*.cjs'", "compile": "tsc", "coverage": "c8 report --check-coverage", "fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js' && standardx --fix '**/*.cjs'", "postcompile": "npm run build:cjs", "postest": "check", "precompile": "<PERSON><PERSON><PERSON> build", "prepare": "npm run compile", "pretest": "rimraf build && tsc -p tsconfig.test.json && cross-env NODE_ENV=test npm run build:cjs", "test": "c8 mocha ./test/*.cjs", "test:esm": "c8 mocha ./test/esm/cliui-test.mjs"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "type": "module", "version": "7.0.4"}