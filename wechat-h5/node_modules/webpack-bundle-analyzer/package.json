{"name": "webpack-bundle-analyzer", "version": "3.9.0", "description": "Webpack plugin and CLI utility that represents bundle content as convenient interactive zoomable treemap", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/webpack-contrib/webpack-bundle-analyzer", "changelog": "https://github.com/webpack-contrib/webpack-bundle-analyzer/blob/master/CHANGELOG.md", "bugs": {"url": "https://github.com/webpack-contrib/webpack-bundle-analyzer/issues"}, "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/webpack-bundle-analyzer.git"}, "main": "lib/index.js", "bin": "lib/bin/analyzer.js", "engines": {"node": ">= 6.14.4"}, "scripts": {"start": "gulp watch", "build": "gulp build", "npm-publish": "npm run lint && npm run build && npm test && npm publish", "lint": "eslint --ext js,jsx .", "test": "mocha --exit --require @babel/register", "test-dev": "mocha --watch --require @babel/register"}, "files": ["public", "lib", "src", "views"], "dependencies": {"acorn": "^7.1.1", "acorn-walk": "^7.1.1", "bfj": "^6.1.1", "chalk": "^2.4.1", "commander": "^2.18.0", "ejs": "^2.6.1", "express": "^4.16.3", "filesize": "^3.6.1", "gzip-size": "^5.0.0", "lodash": "^4.17.19", "mkdirp": "^0.5.1", "opener": "^1.5.1", "ws": "^6.0.0"}, "devDependencies": {"@babel/core": "7.4.3", "@babel/plugin-proposal-class-properties": "7.4.0", "@babel/plugin-proposal-decorators": "7.4.0", "@babel/plugin-transform-runtime": "7.4.3", "@babel/polyfill": "7.4.3", "@babel/preset-env": "7.4.3", "@babel/preset-react": "7.0.0", "@babel/register": "7.4.0", "@babel/runtime": "7.4.3", "autoprefixer": "9.5.1", "babel-eslint": "10.0.1", "babel-loader": "8.0.5", "babel-plugin-lodash": "3.3.4", "chai": "4.2.0", "chai-subset": "1.6.0", "classnames": "2.2.6", "core-js": "2.6.5", "css-loader": "2.1.1", "cssnano": "4.1.10", "del": "4.1.0", "eslint": "5.16.0", "eslint-config-th0r": "2.0.0", "eslint-config-th0r-react": "2.0.0", "eslint-plugin-react": "7.12.4", "exports-loader": "0.7.0", "gulp": "4.0.2", "gulp-babel": "8.0.0", "mobx": "5.9.4", "mobx-preact": "3.0.0", "mocha": "6.2.2", "nightmare": "3.0.1", "postcss-icss-values": "2.0.2", "postcss-loader": "3.0.0", "preact": "8.4.2", "stream-combiner2": "1.1.1", "style-loader": "0.23.1", "terser-webpack-plugin": "1.4.3", "url-loader": "1.1.2", "webpack": "4.42.0", "webpack-cli": "3.3.11", "webpack-dev-server": "3.10.3"}, "keywords": ["webpack", "bundle", "analyzer", "modules", "size", "interactive", "chart", "treemap", "zoomable", "zoom"]}