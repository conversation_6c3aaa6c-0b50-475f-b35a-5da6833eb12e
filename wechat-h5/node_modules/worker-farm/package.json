{"name": "worker-farm", "description": "Distribute processing tasks to child processes with an über-simple API and baked-in durability & custom concurrency options.", "version": "1.7.0", "homepage": "https://github.com/rvagg/node-worker-farm", "authors": ["<PERSON> Vagg @rvagg <<EMAIL>> (https://github.com/rvagg)"], "keywords": ["worker", "child", "processing", "farm"], "main": "./lib/index.js", "repository": {"type": "git", "url": "https://github.com/rvagg/node-worker-farm.git"}, "dependencies": {"errno": "~0.1.7"}, "devDependencies": {"tape": "~4.10.1"}, "scripts": {"test": "node ./tests/"}, "types": "./index.d.ts", "license": "MIT"}