{"name": "webpack-log", "version": "2.0.0", "description": "A common logger for the webpack ecosystem", "main": "src/index.js", "files": ["src"], "engines": {"node": ">= 6"}, "scripts": {"lint": "eslint src test", "test": "nyc --reporter l<PERSON>von<PERSON> mocha --full-trace --check-leaks", "release": "standard-version"}, "dependencies": {"ansi-colors": "^3.0.0", "uuid": "^3.3.2"}, "devDependencies": {"assert": "^1.4.1", "eslint": "^5.4.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-import": "^2.14.0", "mocha": "^5.2.0", "nyc": "^12.0.2", "sinon": "^6.1.5", "standard-version": "^4.4.0", "strip-ansi": "^4.0.0"}, "author": "<PERSON> <<EMAIL>>", "issues": "https://github.com/webpack-contrib/webpack-log/issues", "homepage": "https://github.com/webpack-contrib/webpack-log#readme", "repository": "https://github.com/webpack-contrib/webpack-log.git", "license": "MIT"}