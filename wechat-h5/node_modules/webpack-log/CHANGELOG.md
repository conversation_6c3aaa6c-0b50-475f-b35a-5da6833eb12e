# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

<a name="2.0.0"></a>
# [2.0.0](https://github.com/webpack-contrib/webpack-log/compare/v1.2.0...v2.0.0) (2018-08-23)


### Code Refactoring

* **package:** replace `chalk` with `ansi-colors` (`dependencies`) ([#4](https://github.com/webpack-contrib/webpack-log/issues/4)) ([5b793d4](https://github.com/webpack-contrib/webpack-log/commit/5b793d4))


### BREAKING CHANGES

* **package:** `module.exports.chalk` was renamed to `module.exports.colors`
