{"name": "vue-lazyload", "version": "1.2.3", "description": "Vue module for lazy-loading images in your vue.js applications.", "main": "vue-lazyload.js", "unpkg": "vue-lazyload.js", "scripts": {"build": "node build", "lint": "eslint ./src", "test": "karma start", "test:debug": "cross-env DEBUG=true karma start"}, "dependencies": {}, "repository": {"type": "git", "url": "https://github.com/hilongjw/vue-lazyload.git"}, "keywords": ["vue-lazyload", "vue", "lazyload", "vue-directive"], "author": "Awe <<EMAIL>>", "bugs": {"url": "https://github.com/hilongjw/vue-lazyload/issues"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "license": "MIT", "devDependencies": {"assign-deep": "^0.4.7", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-plugin-external-helpers": "^6.22.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-stage-0": "^6.24.1", "babel-register": "^6.26.0", "chai": "^4.1.2", "eslint": "^4.13.1", "eslint-config-standard": "^11.0.0-beta.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-node": "^5.2.1", "eslint-plugin-promise": "^3.6.0", "eslint-plugin-standard": "^3.0.1", "karma": "^1.7.1", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.1", "karma-expect": "^1.1.3", "karma-mocha": "^1.3.0", "karma-rollup-preprocessor": "^5.0.2", "mocha": "^4.0.1", "puppeteer": "^0.13.0", "rollup": "^0.51.1", "rollup-plugin-babel": "^2.6.1", "rollup-plugin-commonjs": "^8.4.1", "rollup-plugin-node-resolve": "^3.2.0", "rollup-plugin-replace": "^2.0.0", "rollup-plugin-uglify": "^1.0.1", "vue": "^2.5.13"}}