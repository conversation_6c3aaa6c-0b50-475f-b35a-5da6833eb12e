#!/bin/bash

# 微信H5项目问题诊断脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# 检查本地环境
check_local_env() {
    print_header "检查本地环境"
    
    # 检查Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node -v)
        print_success "Node.js: $NODE_VERSION"
    else
        print_error "Node.js 未安装"
    fi
    
    # 检查yarn
    if command -v yarn &> /dev/null; then
        YARN_VERSION=$(yarn -v)
        print_success "Yarn: $YARN_VERSION"
    else
        print_error "Yarn 未安装"
    fi
    
    # 检查项目文件
    if [ -f "package.json" ]; then
        print_success "package.json 存在"
    else
        print_error "package.json 不存在"
    fi
    
    if [ -f "vue.config.js" ]; then
        print_success "vue.config.js 存在"
    else
        print_error "vue.config.js 不存在"
    fi
    
    echo ""
}

# 检查项目配置
check_project_config() {
    print_header "检查项目配置"
    
    # 检查环境变量文件
    if [ -f ".env.production" ]; then
        print_success ".env.production 存在"
        print_info "生产环境配置："
        cat .env.production | grep -E "VUE_APP_" | sed 's/^/  /'
    else
        print_error ".env.production 不存在"
    fi
    
    if [ -f ".env.development" ]; then
        print_success ".env.development 存在"
    else
        print_error ".env.development 不存在"
    fi
    
    # 检查微信验证文件
    VERIFY_FILES=$(find public/ -name "MP_verify_*.txt" 2>/dev/null || true)
    if [ -n "$VERIFY_FILES" ]; then
        print_success "微信验证文件存在: $VERIFY_FILES"
    else
        print_warning "未找到微信验证文件 (MP_verify_*.txt)"
    fi
    
    echo ""
}

# 检查依赖
check_dependencies() {
    print_header "检查项目依赖"
    
    if [ -d "node_modules" ]; then
        print_success "node_modules 目录存在"
        
        # 检查关键依赖
        DEPS=("vue" "vant" "axios" "vue-router" "vuex")
        for dep in "${DEPS[@]}"; do
            if [ -d "node_modules/$dep" ]; then
                print_success "$dep 已安装"
            else
                print_error "$dep 未安装"
            fi
        done
    else
        print_error "node_modules 目录不存在，请运行 yarn install"
    fi
    
    echo ""
}

# 检查构建
check_build() {
    print_header "检查构建状态"
    
    if [ -d "dist" ]; then
        print_success "dist 目录存在"
        
        if [ -f "dist/index.html" ]; then
            print_success "index.html 存在"
        else
            print_error "index.html 不存在"
        fi
        
        if [ -d "dist/static" ]; then
            print_success "static 目录存在"
        else
            print_error "static 目录不存在"
        fi
        
        # 检查文件大小
        DIST_SIZE=$(du -sh dist/ 2>/dev/null | cut -f1 || echo "未知")
        print_info "构建产物大小: $DIST_SIZE"
    else
        print_warning "dist 目录不存在，请运行构建命令"
    fi
    
    echo ""
}

# 检查远程部署
check_remote_deployment() {
    print_header "检查远程部署"
    
    DOMAIN="m.dboss.pro"
    
    # 检查基础页面
    print_info "检查 https://$DOMAIN/"
    if curl -s -I "https://$DOMAIN/" | grep -q "200 OK"; then
        print_success "基础页面可访问"
    else
        print_error "基础页面无法访问"
    fi
    
    # 检查测试页面
    print_info "检查 https://$DOMAIN/test"
    if curl -s -I "https://$DOMAIN/test" | grep -q "200 OK"; then
        print_success "测试页面可访问"
    else
        print_error "测试页面无法访问"
    fi
    
    # 检查绑定页面
    print_info "检查 https://$DOMAIN/store-wechat-bind"
    if curl -s -I "https://$DOMAIN/store-wechat-bind" | grep -q "200 OK"; then
        print_success "绑定页面可访问"
    else
        print_error "绑定页面无法访问"
    fi
    
    # 检查微信验证文件
    VERIFY_FILES=$(find public/ -name "MP_verify_*.txt" 2>/dev/null || true)
    if [ -n "$VERIFY_FILES" ]; then
        for file in $VERIFY_FILES; do
            filename=$(basename "$file")
            print_info "检查 https://$DOMAIN/$filename"
            if curl -s -I "https://$DOMAIN/$filename" | grep -q "200 OK"; then
                print_success "微信验证文件可访问: $filename"
            else
                print_error "微信验证文件无法访问: $filename"
            fi
        done
    fi
    
    echo ""
}

# 运行本地测试
run_local_test() {
    print_header "运行本地测试"
    
    if [ -d "node_modules" ] && [ -f "package.json" ]; then
        print_info "启动开发服务器进行测试..."
        print_info "请在浏览器中访问以下地址进行测试："
        print_info "  - http://localhost:8080/test"
        print_info "  - http://localhost:8080/store-wechat-bind?token=test&type=WECHAT_OFFIACCOUNT_OPEN_ID"
        print_info "按 Ctrl+C 停止服务器"
        
        yarn serve
    else
        print_error "无法启动本地测试，请先安装依赖"
    fi
}

# 生成诊断报告
generate_report() {
    print_header "生成诊断报告"
    
    REPORT_FILE="diagnosis_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "微信H5项目诊断报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo ""
        
        echo "本地环境:"
        node -v 2>/dev/null || echo "Node.js: 未安装"
        yarn -v 2>/dev/null || echo "Yarn: 未安装"
        echo ""
        
        echo "项目文件:"
        ls -la | grep -E "\.(json|js|vue)$|^d.*"
        echo ""
        
        echo "环境配置:"
        if [ -f ".env.production" ]; then
            cat .env.production
        else
            echo ".env.production 不存在"
        fi
        echo ""
        
        echo "构建状态:"
        if [ -d "dist" ]; then
            ls -la dist/
        else
            echo "dist 目录不存在"
        fi
        
    } > "$REPORT_FILE"
    
    print_success "诊断报告已生成: $REPORT_FILE"
}

# 显示帮助
show_help() {
    echo "微信H5项目诊断脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示帮助信息"
    echo "  -l, --local     检查本地环境"
    echo "  -r, --remote    检查远程部署"
    echo "  -t, --test      运行本地测试"
    echo "  -a, --all       运行完整诊断"
    echo "  --report        生成诊断报告"
    echo ""
    echo "示例:"
    echo "  $0 -a           # 运行完整诊断"
    echo "  $0 -l           # 仅检查本地环境"
    echo "  $0 -r           # 仅检查远程部署"
    echo "  $0 -t           # 运行本地测试"
}

# 主函数
main() {
    case "${1:-all}" in
        -h|--help)
            show_help
            ;;
        -l|--local)
            check_local_env
            check_project_config
            check_dependencies
            check_build
            ;;
        -r|--remote)
            check_remote_deployment
            ;;
        -t|--test)
            run_local_test
            ;;
        --report)
            generate_report
            ;;
        -a|--all|*)
            check_local_env
            check_project_config
            check_dependencies
            check_build
            check_remote_deployment
            ;;
    esac
}

# 执行主函数
main "$@"
