#!/bin/bash

# 得宝商城微信H5项目部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Node.js环境
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    NODE_VERSION=$(node -v)
    print_message "Node.js 版本: $NODE_VERSION"
}

# 检查npm环境
check_npm() {
    if ! command -v npm &> /dev/null; then
        print_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    NPM_VERSION=$(npm -v)
    print_message "npm 版本: $NPM_VERSION"
}

# 安装依赖
install_dependencies() {
    print_message "正在安装项目依赖..."
    
    if [ -f "package-lock.json" ]; then
        npm ci
    else
        npm install
    fi
    
    print_message "依赖安装完成"
}

# 构建项目
build_project() {
    local env=${1:-production}
    
    print_message "正在构建项目 (环境: $env)..."
    
    case $env in
        "development")
            npm run build
            ;;
        "test")
            npm run build:test
            ;;
        "production")
            npm run build:prod
            ;;
        *)
            print_error "未知的环境: $env"
            exit 1
            ;;
    esac
    
    print_message "项目构建完成"
}

# 检查构建结果
check_build() {
    if [ ! -d "dist" ]; then
        print_error "构建失败，dist 目录不存在"
        exit 1
    fi
    
    if [ ! -f "dist/index.html" ]; then
        print_error "构建失败，index.html 文件不存在"
        exit 1
    fi
    
    print_message "构建结果检查通过"
}

# 压缩构建文件
compress_build() {
    print_message "正在压缩构建文件..."
    
    if command -v tar &> /dev/null; then
        tar -czf "wechat-h5-$(date +%Y%m%d-%H%M%S).tar.gz" -C dist .
        print_message "构建文件已压缩"
    else
        print_warning "tar 命令不可用，跳过压缩步骤"
    fi
}

# 显示帮助信息
show_help() {
    echo "得宝商城微信H5项目部署脚本"
    echo ""
    echo "用法: $0 [选项] [环境]"
    echo ""
    echo "环境:"
    echo "  development    开发环境 (默认)"
    echo "  test          测试环境"
    echo "  production    生产环境"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -c, --clean    清理 node_modules 和 dist"
    echo "  -i, --install  仅安装依赖"
    echo "  -b, --build    仅构建项目"
    echo "  --compress     压缩构建文件"
    echo ""
    echo "示例:"
    echo "  $0                    # 完整部署 (生产环境)"
    echo "  $0 test              # 测试环境部署"
    echo "  $0 -c                # 清理项目"
    echo "  $0 -i                # 仅安装依赖"
    echo "  $0 -b production     # 仅构建生产版本"
}

# 清理项目
clean_project() {
    print_message "正在清理项目..."
    
    if [ -d "node_modules" ]; then
        rm -rf node_modules
        print_message "已删除 node_modules"
    fi
    
    if [ -d "dist" ]; then
        rm -rf dist
        print_message "已删除 dist"
    fi
    
    print_message "项目清理完成"
}

# 主函数
main() {
    local env="production"
    local only_install=false
    local only_build=false
    local should_clean=false
    local should_compress=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--clean)
                should_clean=true
                shift
                ;;
            -i|--install)
                only_install=true
                shift
                ;;
            -b|--build)
                only_build=true
                shift
                ;;
            --compress)
                should_compress=true
                shift
                ;;
            development|test|production)
                env=$1
                shift
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_message "开始部署得宝商城微信H5项目"
    
    # 清理项目
    if [ "$should_clean" = true ]; then
        clean_project
        if [ "$only_install" = false ] && [ "$only_build" = false ]; then
            exit 0
        fi
    fi
    
    # 检查环境
    check_node
    check_npm
    
    # 仅安装依赖
    if [ "$only_install" = true ]; then
        install_dependencies
        exit 0
    fi
    
    # 仅构建项目
    if [ "$only_build" = true ]; then
        build_project $env
        check_build
        if [ "$should_compress" = true ]; then
            compress_build
        fi
        exit 0
    fi
    
    # 完整部署流程
    install_dependencies
    build_project $env
    check_build
    
    if [ "$should_compress" = true ]; then
        compress_build
    fi
    
    print_message "部署完成！"
    print_message "构建文件位于 dist/ 目录"
    print_message "请将 dist/ 目录的内容部署到Web服务器"
}

# 执行主函数
main "$@"
