<template>
  <div class="wechat-debug-page">
    <van-nav-bar title="微信授权调试" left-arrow @click-left="$router.back()" />
    
    <div class="debug-content">
      <!-- 快速操作 -->
      <van-cell-group title="快速操作">
        <van-cell title="运行完整诊断" is-link @click="runDiagnosis" />
        <van-cell title="测试授权URL构建" is-link @click="testAuthUrl" />
        <van-cell title="清除所有缓存" is-link @click="clearCache" />
        <van-cell title="复制当前页面信息" is-link @click="copyPageInfo" />
      </van-cell-group>
      
      <!-- 实时信息 -->
      <van-cell-group title="实时信息">
        <van-cell title="当前时间" :value="currentTime" />
        <van-cell title="页面URL" :label="currentUrl" />
        <van-cell title="微信环境" :value="isWechat ? '是' : '否'" />
        <van-cell title="User Agent" :label="userAgent" />
      </van-cell-group>
      
      <!-- 环境配置 -->
      <van-cell-group title="环境配置">
        <van-cell title="AppId" :value="appId" />
        <van-cell title="API地址" :value="apiUrl" />
        <van-cell title="当前域名" :value="currentDomain" />
        <van-cell title="协议" :value="protocol" />
      </van-cell-group>
      
      <!-- URL参数 -->
      <van-cell-group title="URL参数" v-if="urlParams && Object.keys(urlParams).length > 0">
        <van-cell 
          v-for="(value, key) in urlParams" 
          :key="key"
          :title="key" 
          :value="value" 
        />
      </van-cell-group>
      
      <!-- 微信授权信息 -->
      <van-cell-group title="微信授权信息" v-if="authInfo">
        <van-cell title="授权Code" :value="authInfo.code || '无'" />
        <van-cell title="State参数" :value="authInfo.state || '无'" />
        <van-cell title="错误信息" :value="authInfo.error || '无'" />
        <van-cell title="是否回调" :value="authInfo.isCallback ? '是' : '否'" />
      </van-cell-group>
      
      <!-- 诊断结果 -->
      <div v-if="diagnosisResult">
        <van-divider>诊断结果</van-divider>
        
        <!-- 配置状态 -->
        <van-cell-group title="配置检查">
          <van-cell 
            title="AppId配置" 
            :value="diagnosisResult.config.appId ? '已配置' : '未配置'"
            :label="diagnosisResult.config.appId"
          />
          <van-cell 
            title="域名格式" 
            :value="isValidDomain ? '正确' : '需要检查'"
            :label="diagnosisResult.config.domain"
          />
          <van-cell 
            title="HTTPS状态" 
            :value="diagnosisResult.config.protocol === 'https:' ? '已启用' : '未启用'"
          />
        </van-cell-group>
        
        <!-- 建议列表 -->
        <van-cell-group title="诊断建议" v-if="diagnosisResult.recommendations.length > 0">
          <van-cell 
            v-for="(recommendation, index) in diagnosisResult.recommendations" 
            :key="index"
            :title="`建议 ${index + 1}`"
            :label="recommendation"
            icon="warning-o"
          />
        </van-cell-group>
        
        <!-- 配置指南 -->
        <van-cell-group title="配置指南">
          <van-cell 
            title="微信公众号后台配置" 
            label="点击查看详细配置步骤"
            is-link
            @click="showConfigSteps"
          />
          <van-cell 
            title="需要配置的域名" 
            :value="diagnosisResult.config.domain"
            @click="copyText(diagnosisResult.config.domain)"
          />
          <van-cell 
            title="验证文件检查" 
            label="点击检查验证文件是否可访问"
            is-link
            @click="checkVerifyFile"
          />
        </van-cell-group>
      </div>
      
      <!-- 错误日志 -->
      <van-cell-group title="错误日志" v-if="errorLogs.length > 0">
        <van-cell 
          v-for="(log, index) in errorLogs" 
          :key="index"
          :title="log.time"
          :label="log.message"
          :value="log.type"
          :class="`log-${log.type}`"
        />
        <van-cell title="清除日志" is-link @click="clearLogs" />
      </van-cell-group>
      
      <!-- 测试工具 -->
      <van-cell-group title="测试工具">
        <van-cell title="测试绑定页面" is-link @click="testBindPage" />
        <van-cell title="模拟授权回调" is-link @click="simulateCallback" />
        <van-cell title="生成测试二维码" is-link @click="generateTestQR" />
      </van-cell-group>
    </div>
  </div>
</template>

<script>
import { getUrlParams } from '@/utils'
import { 
  checkWechatAuthConfig, 
  buildWechatAuthUrl, 
  diagnoseWechatAuth,
  parseWechatAuthCallback 
} from '@/utils/wechat-auth-debug'

export default {
  name: 'WechatDebug',
  data() {
    return {
      currentTime: '',
      currentUrl: '',
      userAgent: '',
      isWechat: false,
      urlParams: null,
      authInfo: null,
      diagnosisResult: null,
      errorLogs: [],
      appId: process.env.VUE_APP_WECHAT_APPID,
      apiUrl: process.env.VUE_APP_API_BASE_URL,
      currentDomain: '',
      protocol: ''
    }
  },
  computed: {
    isValidDomain() {
      return this.currentDomain && !this.currentDomain.includes('localhost')
    }
  },
  mounted() {
    this.initData()
    this.updateTime()
    this.captureErrors()
    
    // 每秒更新时间
    setInterval(() => {
      this.updateTime()
    }, 1000)
  },
  methods: {
    initData() {
      this.currentUrl = window.location.href
      this.userAgent = navigator.userAgent
      this.isWechat = /micromessenger/i.test(navigator.userAgent)
      this.urlParams = getUrlParams()
      this.authInfo = parseWechatAuthCallback()
      this.currentDomain = window.location.host
      this.protocol = window.location.protocol
    },
    
    updateTime() {
      this.currentTime = new Date().toLocaleString()
    },
    
    runDiagnosis() {
      try {
        this.diagnosisResult = diagnoseWechatAuth()
        this.$toast('诊断完成')
        this.addLog('运行完整诊断', 'info')
      } catch (error) {
        this.addLog(`诊断失败: ${error.message}`, 'error')
        this.$toast('诊断失败')
      }
    },
    
    testAuthUrl() {
      try {
        const authInfo = buildWechatAuthUrl('test-token', 'WECHAT_OFFIACCOUNT_OPEN_ID')
        this.addLog('授权URL构建成功', 'info')
        this.$toast('授权URL已构建，请查看日志')
      } catch (error) {
        this.addLog(`授权URL构建失败: ${error.message}`, 'error')
        this.$toast('构建失败')
      }
    },
    
    clearCache() {
      sessionStorage.clear()
      localStorage.clear()
      this.addLog('缓存已清除', 'info')
      this.$toast('缓存已清除')
    },
    
    copyPageInfo() {
      const info = {
        url: this.currentUrl,
        userAgent: this.userAgent,
        isWechat: this.isWechat,
        appId: this.appId,
        domain: this.currentDomain,
        protocol: this.protocol,
        urlParams: this.urlParams,
        authInfo: this.authInfo
      }
      
      this.copyText(JSON.stringify(info, null, 2))
    },
    
    copyText(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$toast('已复制到剪贴板')
        }).catch(() => {
          this.$toast('复制失败')
        })
      } else {
        this.$toast('当前环境不支持复制功能')
      }
    },
    
    showConfigSteps() {
      this.$dialog.alert({
        title: '微信公众号配置步骤',
        message: `1. 登录微信公众平台 (mp.weixin.qq.com)
2. 进入"设置与开发" → "公众号设置" → "功能设置"
3. 在"网页授权域名"中添加: ${this.currentDomain}
4. 下载验证文件 (MP_verify_xxx.txt)
5. 上传验证文件到域名根目录
6. 保存配置并等待生效 (1-10分钟)

注意：域名格式不要包含协议(http/https)和路径`
      })
    },
    
    checkVerifyFile() {
      // 这里可以添加检查验证文件的逻辑
      this.$toast('验证文件检查功能待实现')
    },
    
    testBindPage() {
      this.$router.push('/store-wechat-bind?token=test&type=WECHAT_OFFIACCOUNT_OPEN_ID')
    },
    
    simulateCallback() {
      const testCode = 'test_code_' + Date.now()
      const testState = 'test_state_' + Math.random().toString(36).substring(7)
      this.$router.push(`/store-wechat-bind?code=${testCode}&state=${testState}`)
    },
    
    generateTestQR() {
      this.$toast('测试二维码生成功能待实现')
    },
    
    clearLogs() {
      this.errorLogs = []
      this.$toast('日志已清除')
    },
    
    captureErrors() {
      // 捕获全局错误
      window.addEventListener('error', (event) => {
        this.addLog(`JavaScript错误: ${event.message}`, 'error')
      })
      
      // 捕获Promise错误
      window.addEventListener('unhandledrejection', (event) => {
        this.addLog(`Promise错误: ${event.reason}`, 'error')
      })
    },
    
    addLog(message, type = 'info') {
      const log = {
        time: new Date().toLocaleTimeString(),
        message: message,
        type: type
      }
      this.errorLogs.unshift(log)
      
      // 只保留最近50条日志
      if (this.errorLogs.length > 50) {
        this.errorLogs = this.errorLogs.slice(0, 50)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.wechat-debug-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.debug-content {
  padding: 20px;
  
  .van-cell-group {
    margin-bottom: 15px;
  }
}

.log-error {
  background-color: #fef0f0;
  border-left: 3px solid #f56c6c;
}

.log-warning {
  background-color: #fdf6ec;
  border-left: 3px solid #e6a23c;
}

.log-info {
  background-color: #f4f4f5;
  border-left: 3px solid #909399;
}
</style>
