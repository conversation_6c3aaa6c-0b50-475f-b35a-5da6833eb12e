<template>
  <div class="error-page">
    <div class="error-container">
      <!-- 错误图标 -->
      <div class="error-icon">
        <van-icon :name="errorIcon" size="80" :color="errorColor" />
      </div>
      
      <!-- 错误信息 -->
      <div class="error-content">
        <h1 class="error-title">{{ errorTitle }}</h1>
        <p class="error-message">{{ errorMessage }}</p>
        
        <!-- 解决方案 -->
        <div v-if="solutions.length > 0" class="solutions">
          <h3>解决方案：</h3>
          <ul>
            <li v-for="(solution, index) in solutions" :key="index">
              {{ solution }}
            </li>
          </ul>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="error-actions">
        <van-button 
          v-if="showRetry"
          type="primary" 
          size="large" 
          @click="handleRetry"
          class="action-button"
        >
          重试
        </van-button>
        
        <van-button 
          size="large" 
          @click="handleClose"
          :type="showRetry ? 'default' : 'primary'"
          class="action-button"
        >
          关闭页面
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Error',
  data() {
    return {
      errorType: '',
      customMessage: ''
    }
  },
  computed: {
    errorIcon() {
      switch (this.errorType) {
        case 'env':
          return 'warning-o'
        case 'network':
          return 'wifi-o'
        case 'auth':
          return 'lock'
        default:
          return 'close'
      }
    },
    
    errorColor() {
      switch (this.errorType) {
        case 'env':
          return '#ff976a'
        case 'network':
          return '#1989fa'
        case 'auth':
          return '#ee0a24'
        default:
          return '#ee0a24'
      }
    },
    
    errorTitle() {
      switch (this.errorType) {
        case 'env':
          return '环境不支持'
        case 'network':
          return '网络错误'
        case 'auth':
          return '授权失败'
        default:
          return '页面错误'
      }
    },
    
    errorMessage() {
      if (this.customMessage) {
        return this.customMessage
      }
      
      switch (this.errorType) {
        case 'env':
          return '请在微信中打开此页面'
        case 'network':
          return '网络连接失败，请检查网络设置'
        case 'auth':
          return '授权验证失败，请重新授权'
        default:
          return '页面加载失败，请稍后重试'
      }
    },
    
    solutions() {
      switch (this.errorType) {
        case 'env':
          return [
            '使用微信扫描二维码打开页面',
            '在微信中点击链接访问',
            '确保在微信浏览器环境中使用'
          ]
        case 'network':
          return [
            '检查网络连接是否正常',
            '尝试切换网络环境',
            '稍后重新尝试'
          ]
        case 'auth':
          return [
            '重新扫描二维码',
            '确认微信授权权限',
            '联系客服获取帮助'
          ]
        default:
          return [
            '刷新页面重试',
            '检查网络连接',
            '联系技术支持'
          ]
      }
    },
    
    showRetry() {
      return ['network', 'auth'].includes(this.errorType)
    }
  },
  mounted() {
    // 获取错误类型和消息
    this.errorType = this.$route.query.type || 'default'
    this.customMessage = this.$route.query.message || ''
  },
  methods: {
    // 重试
    handleRetry() {
      // 返回绑定确认页面
      this.$router.replace('/bind-confirm')
    },
    
    // 关闭页面
    handleClose() {
      // 尝试关闭窗口
      if (typeof WeixinJSBridge !== 'undefined') {
        WeixinJSBridge.call('closeWindow')
      } else {
        window.close()
        // 如果无法关闭窗口，提示用户手动关闭
        setTimeout(() => {
          this.$toast('请手动关闭此页面')
        }, 300)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  padding: 20px;
}

.error-container {
  width: 100%;
  max-width: 400px;
  text-align: center;
  background-color: #fff;
  border-radius: 12px;
  padding: 40px 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.error-icon {
  margin-bottom: 30px;
}

.error-content {
  margin-bottom: 40px;
  
  .error-title {
    font-size: 24px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 15px;
  }
  
  .error-message {
    font-size: 16px;
    color: #646566;
    line-height: 1.5;
    margin-bottom: 20px;
  }
  
  .solutions {
    text-align: left;
    background-color: #f7f8fa;
    border-radius: 8px;
    padding: 15px;
    
    h3 {
      font-size: 14px;
      color: #323233;
      margin-bottom: 10px;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        font-size: 14px;
        color: #646566;
        line-height: 1.6;
        margin-bottom: 5px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.error-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  
  .action-button {
    width: 100%;
    height: 50px;
  }
}

// 响应式适配
@media (max-width: 375px) {
  .error-container {
    padding: 30px 20px;
  }
  
  .error-content .error-title {
    font-size: 20px;
  }
}
</style>
