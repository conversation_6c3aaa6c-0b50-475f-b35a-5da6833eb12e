<template>
  <div class="test-page">
    <van-nav-bar title="测试页面" />
    
    <div class="test-content">
      <van-cell-group>
        <van-cell title="项目状态" value="正常运行" />
        <van-cell title="当前时间" :value="currentTime" />
        <van-cell title="当前URL" :value="currentUrl" />
        <van-cell title="用户代理" :value="userAgent" />
        <van-cell title="是否微信环境" :value="isWechat ? '是' : '否'" />
      </van-cell-group>
      
      <div class="test-buttons">
        <van-button type="primary" block @click="testApi">测试API</van-button>
        <van-button type="success" block @click="testNavigation">测试导航</van-button>
        <van-button type="warning" block @click="testError">测试错误页面</van-button>
        <van-button type="info" block @click="testWechatAuth">测试微信授权</van-button>
        <van-button type="default" block @click="diagnoseWechat">诊断微信配置</van-button>
        <van-button type="primary" block @click="openDebugPage">打开专业调试页面</van-button>
      </div>
      
      <div class="url-params" v-if="urlParams">
        <van-divider>URL参数</van-divider>
        <van-cell-group>
          <van-cell
            v-for="(value, key) in urlParams"
            :key="key"
            :title="key"
            :value="value"
          />
        </van-cell-group>
      </div>

      <!-- 微信授权诊断结果 -->
      <div class="diagnosis-result" v-if="diagnosisResult">
        <van-divider>微信授权诊断结果</van-divider>

        <!-- 基础配置 -->
        <van-cell-group title="基础配置">
          <van-cell title="AppId" :value="diagnosisResult.config.appId" />
          <van-cell title="当前域名" :value="diagnosisResult.config.domain" />
          <van-cell title="协议" :value="diagnosisResult.config.protocol" />
          <van-cell title="微信环境" :value="diagnosisResult.config.isWechat ? '是' : '否'" />
        </van-cell-group>

        <!-- 回调参数 -->
        <van-cell-group title="回调参数" v-if="diagnosisResult.callback.isCallback">
          <van-cell title="Code" :value="diagnosisResult.callback.code || '无'" />
          <van-cell title="State" :value="diagnosisResult.callback.state || '无'" />
          <van-cell title="错误" :value="diagnosisResult.callback.error || '无'" />
          <van-cell title="错误描述" :value="diagnosisResult.callback.errorDescription || '无'" />
        </van-cell-group>

        <!-- 建议 -->
        <van-cell-group title="诊断建议" v-if="diagnosisResult.recommendations.length > 0">
          <van-cell
            v-for="(recommendation, index) in diagnosisResult.recommendations"
            :key="index"
            :title="`${index + 1}.`"
            :label="recommendation"
          />
        </van-cell-group>

        <!-- 配置指南 -->
        <van-cell-group title="配置指南">
          <van-cell
            title="微信公众号后台"
            label="设置与开发 → 公众号设置 → 功能设置 → 网页授权域名"
            is-link
            @click="showConfigGuide"
          />
          <van-cell
            title="需要配置的域名"
            :value="diagnosisResult.config.domain"
          />
          <van-cell
            title="验证文件URL"
            :label="`${diagnosisResult.config.protocol}//${diagnosisResult.config.domain}/MP_verify_xxx.txt`"
          />
        </van-cell-group>
      </div>

      <!-- 错误日志 -->
      <div class="error-logs" v-if="errorLogs.length > 0">
        <van-divider>错误日志</van-divider>
        <van-cell-group>
          <van-cell
            v-for="(log, index) in errorLogs"
            :key="index"
            :title="log.time"
            :label="log.message"
            :value="log.type"
          />
        </van-cell-group>
      </div>
    </div>
  </div>
</template>

<script>
import { getUrlParams } from '@/utils'
import {
  checkWechatAuthConfig,
  buildWechatAuthUrl,
  diagnoseWechatAuth,
  parseWechatAuthCallback
} from '@/utils/wechat-auth-debug'

export default {
  name: 'Test',
  data() {
    return {
      currentTime: '',
      currentUrl: '',
      userAgent: '',
      isWechat: false,
      urlParams: null,
      diagnosisResult: null,
      errorLogs: []
    }
  },
  mounted() {
    this.initData()
    this.updateTime()
    this.captureErrors()

    // 每秒更新时间
    setInterval(() => {
      this.updateTime()
    }, 1000)
  },
  methods: {
    initData() {
      this.currentUrl = window.location.href
      this.userAgent = navigator.userAgent
      this.isWechat = /micromessenger/i.test(navigator.userAgent)
      this.urlParams = getUrlParams()
    },
    
    updateTime() {
      this.currentTime = new Date().toLocaleString()
    },
    
    testApi() {
      this.$toast('API测试功能待实现')
    },
    
    testNavigation() {
      this.$router.push('/bind-confirm?token=test&type=WECHAT_OFFIACCOUNT_OPEN_ID')
    },
    
    testError() {
      this.$router.push('/error?type=test&message=这是一个测试错误')
    },

    openDebugPage() {
      this.$router.push('/wechat-debug')
    },

    testWechatAuth() {
      // 构建测试授权URL
      const authInfo = buildWechatAuthUrl('test-token', 'WECHAT_OFFIACCOUNT_OPEN_ID')
      this.$toast('请查看控制台输出的授权信息')
    },

    diagnoseWechat() {
      try {
        // 运行完整诊断
        const report = diagnoseWechatAuth()
        this.diagnosisResult = report
        this.$toast('诊断完成，结果显示在下方')

        // 记录诊断日志
        this.addLog('诊断完成', 'info')

        // 滚动到结果区域
        setTimeout(() => {
          const element = document.querySelector('.diagnosis-result')
          if (element) {
            element.scrollIntoView({ behavior: 'smooth' })
          }
        }, 500)
      } catch (error) {
        this.addLog(`诊断失败: ${error.message}`, 'error')
        this.$toast('诊断失败，请查看错误日志')
      }
    },

    showConfigGuide() {
      this.$dialog.alert({
        title: '微信公众号配置指南',
        message: `1. 登录微信公众平台 (mp.weixin.qq.com)
2. 进入"设置与开发" → "公众号设置" → "功能设置"
3. 在"网页授权域名"中添加: ${this.diagnosisResult.config.domain}
4. 下载验证文件并上传到域名根目录
5. 保存配置并等待生效`
      })
    },

    captureErrors() {
      // 捕获全局错误
      window.addEventListener('error', (event) => {
        this.addLog(`JavaScript错误: ${event.message}`, 'error')
      })

      // 捕获Promise错误
      window.addEventListener('unhandledrejection', (event) => {
        this.addLog(`Promise错误: ${event.reason}`, 'error')
      })

      // 重写console.error
      const originalError = console.error
      console.error = (...args) => {
        this.addLog(args.join(' '), 'error')
        originalError.apply(console, args)
      }
    },

    addLog(message, type = 'info') {
      const log = {
        time: new Date().toLocaleTimeString(),
        message: message,
        type: type
      }
      this.errorLogs.unshift(log)

      // 只保留最近20条日志
      if (this.errorLogs.length > 20) {
        this.errorLogs = this.errorLogs.slice(0, 20)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.test-content {
  padding: 20px;
}

.test-buttons {
  margin: 20px 0;
  
  .van-button {
    margin-bottom: 10px;
  }
}

.url-params,
.diagnosis-result,
.error-logs {
  margin-top: 20px;
}

.diagnosis-result {
  .van-cell-group {
    margin-bottom: 15px;
  }
}

.error-logs {
  .van-cell {
    &[data-type="error"] {
      background-color: #fef0f0;
      border-left: 3px solid #f56c6c;
    }

    &[data-type="warning"] {
      background-color: #fdf6ec;
      border-left: 3px solid #e6a23c;
    }

    &[data-type="info"] {
      background-color: #f4f4f5;
      border-left: 3px solid #909399;
    }
  }
}
</style>
