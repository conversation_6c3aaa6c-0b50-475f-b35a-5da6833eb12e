<template>
  <div class="test-page">
    <van-nav-bar title="测试页面" />
    
    <div class="test-content">
      <van-cell-group>
        <van-cell title="项目状态" value="正常运行" />
        <van-cell title="当前时间" :value="currentTime" />
        <van-cell title="当前URL" :value="currentUrl" />
        <van-cell title="用户代理" :value="userAgent" />
        <van-cell title="是否微信环境" :value="isWechat ? '是' : '否'" />
      </van-cell-group>
      
      <div class="test-buttons">
        <van-button type="primary" block @click="testApi">测试API</van-button>
        <van-button type="success" block @click="testNavigation">测试导航</van-button>
        <van-button type="warning" block @click="testError">测试错误页面</van-button>
        <van-button type="info" block @click="testWechatAuth">测试微信授权</van-button>
        <van-button type="default" block @click="diagnoseWechat">诊断微信配置</van-button>
      </div>
      
      <div class="url-params" v-if="urlParams">
        <van-divider>URL参数</van-divider>
        <van-cell-group>
          <van-cell 
            v-for="(value, key) in urlParams" 
            :key="key"
            :title="key" 
            :value="value" 
          />
        </van-cell-group>
      </div>
    </div>
  </div>
</template>

<script>
import { getUrlParams } from '@/utils'
import {
  checkWechatAuthConfig,
  buildWechatAuthUrl,
  diagnoseWechatAuth,
  parseWechatAuthCallback
} from '@/utils/wechat-auth-debug'

export default {
  name: 'Test',
  data() {
    return {
      currentTime: '',
      currentUrl: '',
      userAgent: '',
      isWechat: false,
      urlParams: null
    }
  },
  mounted() {
    this.initData()
    this.updateTime()
    
    // 每秒更新时间
    setInterval(() => {
      this.updateTime()
    }, 1000)
  },
  methods: {
    initData() {
      this.currentUrl = window.location.href
      this.userAgent = navigator.userAgent
      this.isWechat = /micromessenger/i.test(navigator.userAgent)
      this.urlParams = getUrlParams()
    },
    
    updateTime() {
      this.currentTime = new Date().toLocaleString()
    },
    
    testApi() {
      this.$toast('API测试功能待实现')
    },
    
    testNavigation() {
      this.$router.push('/bind-confirm?token=test&type=WECHAT_OFFIACCOUNT_OPEN_ID')
    },
    
    testError() {
      this.$router.push('/error?type=test&message=这是一个测试错误')
    },

    testWechatAuth() {
      // 构建测试授权URL
      const authInfo = buildWechatAuthUrl('test-token', 'WECHAT_OFFIACCOUNT_OPEN_ID')
      this.$toast('请查看控制台输出的授权信息')
    },

    diagnoseWechat() {
      // 运行完整诊断
      const report = diagnoseWechatAuth()
      this.$toast('诊断完成，请查看控制台')

      // 显示简要结果
      if (report.recommendations.length > 0) {
        setTimeout(() => {
          this.$dialog.alert({
            title: '诊断结果',
            message: report.recommendations.join('\n')
          })
        }, 500)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.test-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.test-content {
  padding: 20px;
}

.test-buttons {
  margin: 20px 0;
  
  .van-button {
    margin-bottom: 10px;
  }
}

.url-params {
  margin-top: 20px;
}
</style>
