<template>
  <div class="bind-confirm-page">
    <!-- 导航栏 -->
    <van-nav-bar 
      title="微信绑定确认" 
      left-arrow 
      @click-left="handleBack"
      :border="false"
      :fixed="true"
      :placeholder="true"
    />

    <!-- 主要内容 -->
    <div class="page-container">
      <!-- Logo区域 -->
      <div class="logo-section">
        <van-icon name="wechat" size="60" color="#07c160" />
        <h1 class="title">微信通知绑定确认</h1>
      </div>

      <!-- 加载状态 -->
      <div v-if="bindStatus.loading" class="loading-section">
        <van-loading size="24px" vertical>正在处理，请稍候...</van-loading>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="bindStatus.error" class="error-section">
        <van-icon name="close" size="60" color="#ee0a24" />
        <h2 class="error-title">{{ bindStatus.errorMessage }}</h2>
        <p class="error-detail">{{ bindStatus.errorDetail }}</p>
        <van-button 
          type="primary" 
          size="large" 
          @click="handleClose"
          class="action-button"
        >
          关闭页面
        </van-button>
      </div>

      <!-- 确认绑定状态 -->
      <div v-else-if="!bindStatus.confirmed" class="confirm-section">
        <div class="bind-info">
          <van-cell-group :border="false" class="info-card">
            <van-cell 
              :title="bindTypeText" 
              :label="bindDescription"
              icon="info-o"
            />
          </van-cell-group>
        </div>
        
        <div class="action-buttons">
          <van-button 
            size="large" 
            @click="handleCancel"
            class="cancel-button"
          >
            取消
          </van-button>
          <van-button 
            type="primary" 
            size="large" 
            @click="handleConfirm"
            :loading="confirming"
            class="confirm-button"
          >
            确认绑定
          </van-button>
        </div>
      </div>

      <!-- 成功状态 -->
      <div v-else class="success-section">
        <van-icon name="checked" size="60" color="#07c160" />
        <h2 class="success-title">绑定成功</h2>
        <p class="success-detail">您已成功绑定{{ bindTypeText }}</p>
        <van-button 
          type="primary" 
          size="large" 
          @click="handleClose"
          class="action-button"
        >
          关闭页面
        </van-button>
      </div>

      <!-- 调试信息 (开发环境) -->
      <div v-if="isDev" class="debug-section">
        <van-divider>调试信息</van-divider>
        <van-cell-group>
          <van-cell title="Token" :value="token || '无'" />
          <van-cell title="UnionType" :value="unionType || '无'" />
          <van-cell title="当前URL" :label="currentUrl" />
          <van-cell title="URL参数" :label="JSON.stringify(urlParams)" />
          <van-cell title="SessionStorage" :label="sessionStorageInfo" />
        </van-cell-group>

        <div v-if="result" class="api-result">
          <van-divider>API响应</van-divider>
          <pre class="debug-info">{{ JSON.stringify(result, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import { getWechatUserInfoByCode, confirmWechatBind } from '@/api/wechat'
import { getUrlParams, cleanUrlParams, generateRandomString } from '@/utils'

export default {
  name: 'BindConfirm',
  data() {
    return {
      token: '',
      unionType: '',
      confirming: false,
      result: null,
      isDev: process.env.NODE_ENV === 'development',
      currentUrl: '',
      urlParams: null
    }
  },
  computed: {
    ...mapState(['bindStatus', 'isWechat']),

    bindTypeText() {
      return this.unionType === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序'
    },

    bindDescription() {
      return '绑定后，您将可以接收订单付款等重要通知'
    },

    sessionStorageInfo() {
      const stateData = sessionStorage.getItem('wechat_auth_state_data')
      return stateData ? JSON.stringify(JSON.parse(stateData), null, 2) : '无'
    }
  },
  mounted() {
    this.currentUrl = window.location.href
    this.urlParams = getUrlParams()
    this.initPage()
  },
  methods: {
    ...mapActions(['setBindStatus', 'resetBindStatus']),
    
    // 初始化页面
    async initPage() {
      this.setBindStatus({ loading: true })

      try {
        // 获取URL参数
        const params = getUrlParams()

        // 检查是否是微信授权回调
        if (params.code && params.state) {
          // 这是微信授权回调，尝试从sessionStorage恢复原始参数
          const savedStateData = sessionStorage.getItem('wechat_auth_state_data')
          if (savedStateData) {
            const stateData = JSON.parse(savedStateData)
            this.token = stateData.token
            this.unionType = stateData.type

            console.log('从授权回调恢复参数:', {
              token: this.token,
              unionType: this.unionType,
              code: params.code,
              state: params.state
            })
          } else {
            throw new Error('授权状态丢失，请重新扫描二维码')
          }
        } else {
          // 这是首次访问，从URL参数获取
          this.token = params.token || ''
          this.unionType = params.type || ''
        }

        // 验证参数
        if (!this.token || !this.unionType) {
          throw new Error('缺少必要的绑定参数，请重新扫描二维码')
        }

        // 验证unionType
        if (!['WECHAT_OFFIACCOUNT_OPEN_ID', 'WECHAT_MP_OPEN_ID'].includes(this.unionType)) {
          throw new Error('绑定类型不正确，请重新扫描二维码')
        }

        this.setBindStatus({ loading: false })
      } catch (error) {
        console.error('页面初始化失败:', error)
        this.setBindStatus({
          loading: false,
          error: true,
          errorMessage: '参数错误',
          errorDetail: error.message
        })
      }
    },

    // 获取微信用户信息（网页授权）
    async getWechatUserInfo() {
      // 检查URL中是否已有授权code
      const params = getUrlParams()
      const code = params.code

      if (code) {
        // 如果已有code，直接使用code获取用户信息
        return await this.getUserInfoByCode(code)
      } else {
        // 如果没有code，跳转到微信授权页面
        this.redirectToWechatAuth()
        throw new Error('正在跳转到微信授权页面...')
      }
    },

    // 跳转到微信网页授权
    redirectToWechatAuth() {
      // 构建回调地址 - 只使用基础URL，不包含查询参数
      const protocol = window.location.protocol
      const host = window.location.host
      const pathname = window.location.pathname

      // 构建干净的回调URL
      const redirectUri = encodeURIComponent(`${protocol}//${host}${pathname}`)

      // 生成state参数，包含原始参数信息
      const state = generateRandomString()
      const stateData = {
        state: state,
        token: this.token,
        type: this.unionType,
        timestamp: Date.now()
      }

      // 保存完整的state数据到sessionStorage
      sessionStorage.setItem('wechat_auth_state_data', JSON.stringify(stateData))

      // 微信网页授权URL
      const appId = process.env.VUE_APP_WECHAT_APPID
      const scope = 'snsapi_userinfo' // 获取用户信息授权

      const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`

      console.log('微信授权跳转信息:', {
        appId,
        redirectUri: decodeURIComponent(redirectUri),
        scope,
        state,
        authUrl
      })

      // 跳转到微信授权页面
      window.location.href = authUrl
    },

    // 通过code获取用户信息
    async getUserInfoByCode(code) {
      try {
        // 验证state参数
        const params = getUrlParams()
        const state = params.state
        const savedStateData = sessionStorage.getItem('wechat_auth_state_data')

        if (savedStateData) {
          const stateData = JSON.parse(savedStateData)

          // 验证state
          if (state && state !== stateData.state) {
            throw new Error('授权验证失败，请重新授权')
          }

          // 确保参数已恢复（通常在initPage中已经恢复了）
          if (!this.token || !this.unionType) {
            this.token = stateData.token
            this.unionType = stateData.type

            console.log('在getUserInfoByCode中恢复参数:', {
              token: this.token,
              unionType: this.unionType,
              state: state
            })
          }
        } else if (state) {
          throw new Error('授权状态丢失，请重新授权')
        }

        // 最终验证参数
        if (!this.token || !this.unionType) {
          throw new Error('绑定参数丢失，请重新扫描二维码')
        }

        // 调用后端API获取用户信息
        const response = await getWechatUserInfoByCode(code)

        if (response.success) {
          const userInfo = response.result

          // 清理URL中的授权参数
          cleanUrlParams(['code', 'state'])

          // 清理sessionStorage
          sessionStorage.removeItem('wechat_auth_state_data')

          return {
            unionId: userInfo.unionid || '',
            openId: userInfo.openid || '',
            nickname: userInfo.nickname || '微信用户',
            avatar: userInfo.headimgurl || '',
            gender: userInfo.sex || 0,
            city: userInfo.city || '',
            province: userInfo.province || '',
            country: userInfo.country || '',
            code: code
          }
        } else {
          throw new Error(response.message || '获取用户信息失败')
        }
      } catch (error) {
        console.error('通过code获取用户信息失败:', error)
        throw error
      }
    },

    // 确认绑定
    async handleConfirm() {
      this.confirming = true

      try {
        console.log('开始确认绑定，当前参数:', {
          token: this.token,
          unionType: this.unionType,
          currentUrl: window.location.href
        })

        // 获取微信用户信息
        const userInfo = await this.getWechatUserInfo()

        // 调用确认绑定API
        const response = await confirmWechatBind({
          bindToken: this.token,
          unionType: this.unionType,
          unionId: userInfo.unionId,
          openId: userInfo.openId,
          code: userInfo.code,
          nickname: userInfo.nickname,
          avatar: userInfo.avatar,
          gender: userInfo.gender,
          city: userInfo.city,
          province: userInfo.province,
          country: userInfo.country
        })

        this.result = response

        if (response.success) {
          this.setBindStatus({ confirmed: true })
        } else {
          this.setBindStatus({
            error: true,
            errorMessage: '绑定失败',
            errorDetail: response.message || '请重新扫描二维码'
          })
        }
      } catch (error) {
        console.error('确认绑定失败:', error)
        this.setBindStatus({
          error: true,
          errorMessage: '绑定失败',
          errorDetail: error.message || '网络错误或服务器异常，请重试'
        })
      } finally {
        this.confirming = false
      }
    },

    // 取消绑定
    handleCancel() {
      this.setBindStatus({
        error: true,
        errorMessage: '已取消绑定',
        errorDetail: '您已取消绑定操作'
      })
    },

    // 返回按钮
    handleBack() {
      this.handleClose()
    },

    // 关闭页面
    handleClose() {
      // 尝试关闭窗口，如果是在微信内打开的页面
      if (typeof WeixinJSBridge !== 'undefined') {
        WeixinJSBridge.call('closeWindow')
      } else {
        window.close()
        // 如果无法关闭窗口，提示用户手动关闭
        setTimeout(() => {
          this.$toast('请手动关闭此页面')
        }, 300)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.bind-confirm-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.page-container {
  padding: 20px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.logo-section {
  text-align: center;
  padding: 40px 0 30px;

  .title {
    margin-top: 15px;
    font-size: 24px;
    font-weight: 500;
    color: #323233;
  }
}

.loading-section {
  text-align: center;
  padding: 60px 0;
}

.error-section,
.success-section {
  text-align: center;
  padding: 40px 0;

  .error-title,
  .success-title {
    margin: 20px 0 10px;
    font-size: 20px;
    font-weight: 500;
    color: #323233;
  }

  .error-detail,
  .success-detail {
    margin-bottom: 30px;
    color: #969799;
    line-height: 1.5;
  }

  .action-button {
    width: 100%;
    margin-top: 20px;
  }
}

.confirm-section {
  .bind-info {
    margin-bottom: 40px;

    .info-card {
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .action-buttons {
    display: flex;
    gap: 15px;

    .cancel-button,
    .confirm-button {
      flex: 1;
      height: 50px;
    }

    .cancel-button {
      background-color: #f7f8fa;
      color: #646566;
      border: 1px solid #ebedf0;
    }
  }
}

.debug-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;

  .debug-info {
    font-size: 12px;
    color: #646566;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

// 响应式适配
@media (max-width: 375px) {
  .page-container {
    padding: 15px;
  }

  .logo-section {
    padding: 30px 0 20px;

    .title {
      font-size: 20px;
    }
  }

  .action-buttons {
    flex-direction: column;
    gap: 10px;

    .cancel-button,
    .confirm-button {
      width: 100%;
    }
  }
}
</style>
