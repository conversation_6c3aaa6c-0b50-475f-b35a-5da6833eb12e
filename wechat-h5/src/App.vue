<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  mounted() {
    // 设置页面标题
    document.title = process.env.VUE_APP_TITLE || '得宝商城微信服务'
    
    // 禁用页面缩放
    this.disableZoom()
    
    // 微信浏览器适配
    this.wechatAdapt()
  },
  methods: {
    // 禁用页面缩放
    disableZoom() {
      document.addEventListener('touchstart', function (event) {
        if (event.touches.length > 1) {
          event.preventDefault()
        }
      })
      
      let lastTouchEnd = 0
      document.addEventListener('touchend', function (event) {
        const now = (new Date()).getTime()
        if (now - lastTouchEnd <= 300) {
          event.preventDefault()
        }
        lastTouchEnd = now
      }, false)
    },
    
    // 微信浏览器适配
    wechatAdapt() {
      // 检测微信浏览器
      const isWechat = /micromessenger/i.test(navigator.userAgent)
      if (isWechat) {
        // 微信浏览器特殊处理
        this.$store.commit('SET_IS_WECHAT', true)
      }
    }
  }
}
</script>

<style lang="less">
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f8f8;
  min-height: 100vh;
}

// 全局样式重置
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  background-color: #f8f8f8;
}

// 微信浏览器适配
.wechat-container {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
