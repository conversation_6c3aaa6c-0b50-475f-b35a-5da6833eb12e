import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/bind-confirm'
  },
  {
    path: '/bind-confirm',
    name: 'BindConfirm',
    component: () => import('@/views/BindConfirm.vue'),
    meta: {
      title: '微信绑定确认',
      requiresWechat: true
    }
  },
  {
    path: '/store-wechat-bind',
    name: 'StoreWechatBind',
    component: () => import('@/views/BindConfirm.vue'),
    meta: {
      title: '微信绑定确认',
      requiresWechat: true
    }
  },
  {
    path: '/error',
    name: 'Error',
    component: () => import('@/views/Error.vue'),
    meta: {
      title: '错误页面'
    }
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/Test.vue'),
    meta: {
      title: '测试页面'
    }
  },
  {
    path: '*',
    redirect: '/error'
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }
  
  // 检查是否需要微信环境
  if (to.meta.requiresWechat) {
    const isWechat = /micromessenger/i.test(navigator.userAgent)
    if (!isWechat) {
      // 非微信环境，跳转到错误页面
      next({
        path: '/error',
        query: { 
          type: 'env',
          message: '请在微信中打开此页面'
        }
      })
      return
    }
  }
  
  next()
})

export default router
