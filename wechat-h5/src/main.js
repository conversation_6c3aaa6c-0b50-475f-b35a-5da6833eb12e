import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// 引入Vant组件
import { 
  Button, 
  Loading, 
  Toast, 
  Dialog, 
  Icon,
  Cell,
  CellGroup,
  Image as VanImage,
  Tag,
  Divider,
  NavBar,
  Popup
} from 'vant'

// 注册Vant组件
Vue.use(Button)
Vue.use(Loading)
Vue.use(Toast)
Vue.use(Dialog)
Vue.use(Icon)
Vue.use(Cell)
Vue.use(CellGroup)
Vue.use(VanImage)
Vue.use(Tag)
Vue.use(Divider)
Vue.use(NavBar)
Vue.use(Popup)

// 引入全局样式
import './styles/index.less'

// 引入工具函数
import * as utils from './utils'
Vue.prototype.$utils = utils

// 全局配置
Vue.config.productionTip = false

// 全局错误处理
Vue.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info)
  Toast.fail('系统错误，请稍后重试')
}

// 创建Vue实例
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
