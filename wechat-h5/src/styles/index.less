// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f8f8;
}

// 清除默认样式
ul, ol {
  list-style: none;
}

a {
  text-decoration: none;
  color: inherit;
}

button {
  border: none;
  outline: none;
  background: none;
}

input, textarea {
  border: none;
  outline: none;
  background: none;
}

// 通用类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

// 间距类
.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mt-30 { margin-top: 30px; }

.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }

.ml-10 { margin-left: 10px; }
.ml-20 { margin-left: 20px; }

.mr-10 { margin-right: 10px; }
.mr-20 { margin-right: 20px; }

.pt-10 { padding-top: 10px; }
.pt-20 { padding-top: 20px; }
.pt-30 { padding-top: 30px; }

.pb-10 { padding-bottom: 10px; }
.pb-20 { padding-bottom: 20px; }
.pb-30 { padding-bottom: 30px; }

.pl-10 { padding-left: 10px; }
.pl-20 { padding-left: 20px; }

.pr-10 { padding-right: 10px; }
.pr-20 { padding-right: 20px; }

// 颜色类
.text-primary { color: #07c160; }
.text-success { color: #07c160; }
.text-warning { color: #ff976a; }
.text-danger { color: #ee0a24; }
.text-gray { color: #969799; }
.text-dark { color: #323233; }

// 微信风格
.wechat-green {
  color: #07c160;
}

.wechat-bg {
  background-color: #07c160;
}

// 页面容器
.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 内容容器
.content-container {
  background-color: #fff;
  border-radius: 8px;
  margin: 20px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

// 响应式
@media (max-width: 375px) {
  .content-container {
    margin: 10px;
    padding: 15px;
  }
}
