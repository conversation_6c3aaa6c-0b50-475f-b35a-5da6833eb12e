<template>
  <div class="empty-state">
    <div class="empty-icon">
      <van-icon :name="icon" size="60" color="#c8c9cc" />
    </div>
    <div class="empty-text">
      <h3 class="empty-title">{{ title }}</h3>
      <p class="empty-description">{{ description }}</p>
    </div>
    <div v-if="showAction" class="empty-action">
      <van-button 
        :type="actionType" 
        size="small" 
        @click="handleAction"
      >
        {{ actionText }}
      </van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmptyState',
  props: {
    icon: {
      type: String,
      default: 'warning-o'
    },
    title: {
      type: String,
      default: '暂无数据'
    },
    description: {
      type: String,
      default: '暂时没有相关内容'
    },
    showAction: {
      type: Boolean,
      default: false
    },
    actionText: {
      type: String,
      default: '重试'
    },
    actionType: {
      type: String,
      default: 'primary'
    }
  },
  methods: {
    handleAction() {
      this.$emit('action')
    }
  }
}
</script>

<style lang="less" scoped>
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  margin-bottom: 20px;
}

.empty-text {
  margin-bottom: 30px;
  
  .empty-title {
    font-size: 16px;
    color: #323233;
    margin-bottom: 8px;
    font-weight: 500;
  }
  
  .empty-description {
    font-size: 14px;
    color: #969799;
    line-height: 1.5;
    margin: 0;
  }
}

.empty-action {
  // 按钮样式由Vant提供
}
</style>
