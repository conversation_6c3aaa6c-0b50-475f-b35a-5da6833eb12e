import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    // 是否在微信环境中
    isWechat: false,
    
    // 用户信息
    userInfo: null,
    
    // 绑定状态
    bindStatus: {
      loading: false,
      confirmed: false,
      error: false,
      errorMessage: '',
      errorDetail: ''
    },
    
    // 页面加载状态
    pageLoading: false
  },
  
  mutations: {
    // 设置微信环境
    SET_IS_WECHAT(state, isWechat) {
      state.isWechat = isWechat
    },
    
    // 设置用户信息
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo
    },
    
    // 设置绑定状态
    SET_BIND_STATUS(state, status) {
      state.bindStatus = { ...state.bindStatus, ...status }
    },
    
    // 重置绑定状态
    RESET_BIND_STATUS(state) {
      state.bindStatus = {
        loading: false,
        confirmed: false,
        error: false,
        errorMessage: '',
        errorDetail: ''
      }
    },
    
    // 设置页面加载状态
    SET_PAGE_LOADING(state, loading) {
      state.pageLoading = loading
    }
  },
  
  actions: {
    // 设置用户信息
    setUserInfo({ commit }, userInfo) {
      commit('SET_USER_INFO', userInfo)
    },
    
    // 设置绑定状态
    setBindStatus({ commit }, status) {
      commit('SET_BIND_STATUS', status)
    },
    
    // 重置绑定状态
    resetBindStatus({ commit }) {
      commit('RESET_BIND_STATUS')
    }
  },
  
  getters: {
    // 是否在微信环境中
    isWechat: state => state.isWechat,
    
    // 获取用户信息
    userInfo: state => state.userInfo,
    
    // 获取绑定状态
    bindStatus: state => state.bindStatus,
    
    // 是否正在加载
    isLoading: state => state.bindStatus.loading || state.pageLoading
  }
})
