/**
 * 微信授权调试工具
 */

/**
 * 检查微信授权配置
 */
export function checkWechatAuthConfig() {
  const config = {
    appId: process.env.VUE_APP_WECHAT_APPID,
    domain: window.location.host,
    protocol: window.location.protocol,
    currentUrl: window.location.href,
    userAgent: navigator.userAgent,
    isWechat: /micromessenger/i.test(navigator.userAgent)
  }
  
  console.group('🔍 微信授权配置检查')
  console.log('AppId:', config.appId)
  console.log('当前域名:', config.domain)
  console.log('协议:', config.protocol)
  console.log('完整URL:', config.currentUrl)
  console.log('是否微信环境:', config.isWechat)
  console.log('User Agent:', config.userAgent)
  console.groupEnd()
  
  return config
}

/**
 * 构建微信授权URL（用于调试）
 */
export function buildWechatAuthUrl(token, unionType) {
  const protocol = window.location.protocol
  const host = window.location.host
  const pathname = window.location.pathname
  
  // 构建回调URL
  const redirectUri = `${protocol}//${host}${pathname}`
  const encodedRedirectUri = encodeURIComponent(redirectUri)
  
  // 生成state
  const state = Math.random().toString(36).substring(2, 15)
  
  const appId = process.env.VUE_APP_WECHAT_APPID
  const scope = 'snsapi_userinfo'
  
  const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${encodedRedirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`
  
  console.group('🔗 微信授权URL构建')
  console.log('AppId:', appId)
  console.log('回调地址:', redirectUri)
  console.log('编码后回调地址:', encodedRedirectUri)
  console.log('Scope:', scope)
  console.log('State:', state)
  console.log('完整授权URL:', authUrl)
  console.groupEnd()
  
  return {
    appId,
    redirectUri,
    encodedRedirectUri,
    scope,
    state,
    authUrl
  }
}

/**
 * 检查微信公众号后台配置
 */
export function checkWechatBackendConfig() {
  const domain = window.location.host
  const protocol = window.location.protocol
  
  const tips = [
    '📋 微信公众号后台配置检查清单:',
    '',
    '1. 登录微信公众平台 (mp.weixin.qq.com)',
    '2. 进入"设置与开发" → "公众号设置" → "功能设置"',
    '3. 在"网页授权域名"中添加以下域名:',
    `   ${domain}`,
    '',
    '⚠️ 注意事项:',
    '- 域名不要包含协议 (http:// 或 https://)',
    '- 域名不要包含路径',
    '- 确保域名已备案且可正常访问',
    '- 需要上传验证文件到域名根目录',
    '',
    '4. 验证文件配置:',
    `   下载验证文件并上传到: ${protocol}//${domain}/MP_verify_xxx.txt`,
    '',
    '5. 保存配置并等待生效 (通常几分钟内生效)'
  ]
  
  console.group('⚙️ 微信公众号后台配置指南')
  tips.forEach(tip => console.log(tip))
  console.groupEnd()
  
  return {
    domain,
    protocol,
    verifyFileUrl: `${protocol}//${domain}/MP_verify_xxx.txt`,
    configUrl: 'https://mp.weixin.qq.com'
  }
}

/**
 * 解析微信授权回调参数
 */
export function parseWechatAuthCallback() {
  const urlParams = new URLSearchParams(window.location.search)
  const code = urlParams.get('code')
  const state = urlParams.get('state')
  const error = urlParams.get('error')
  const errorDescription = urlParams.get('error_description')
  
  const result = {
    code,
    state,
    error,
    errorDescription,
    hasCode: !!code,
    hasError: !!error,
    isCallback: !!(code || error)
  }
  
  if (result.isCallback) {
    console.group('📥 微信授权回调解析')
    console.log('Code:', code)
    console.log('State:', state)
    if (error) {
      console.error('Error:', error)
      console.error('Error Description:', errorDescription)
    }
    console.log('回调结果:', result)
    console.groupEnd()
  }
  
  return result
}

/**
 * 微信授权错误码说明
 */
export function getWechatAuthErrorInfo(errorCode) {
  const errorMap = {
    '10003': {
      code: '10003',
      message: 'redirect_uri域名与后台配置不一致',
      solution: [
        '1. 检查微信公众号后台是否配置了正确的授权域名',
        '2. 确保域名格式正确（不包含协议和路径）',
        '3. 确保域名已备案且可正常访问',
        '4. 检查是否上传了域名验证文件'
      ]
    },
    '10004': {
      code: '10004',
      message: '此公众号被封禁',
      solution: ['联系微信客服解封公众号']
    },
    '10005': {
      code: '10005',
      message: '此公众号并没有这些scope的权限',
      solution: ['检查公众号类型和权限，确保有网页授权权限']
    },
    '10006': {
      code: '10006',
      message: '必须关注此测试号才能授权',
      solution: ['关注测试号或使用正式公众号']
    },
    '10009': {
      code: '10009',
      message: '操作太频繁了，请稍后重试',
      solution: ['稍后重试，避免频繁操作']
    },
    '10010': {
      code: '10010',
      message: 'scope不能为空',
      solution: ['检查授权scope参数']
    },
    '10011': {
      code: '10011',
      message: 'redirect_uri不能为空',
      solution: ['检查redirect_uri参数']
    },
    '10012': {
      code: '10012',
      message: 'appid不能为空',
      solution: ['检查appid参数配置']
    },
    '10013': {
      code: '10013',
      message: 'state不能为空',
      solution: ['检查state参数生成']
    },
    '10015': {
      code: '10015',
      message: '公众号未授权第三方平台',
      solution: ['检查第三方平台授权配置']
    },
    '10016': {
      code: '10016',
      message: '不支持微信开放平台的Appid',
      solution: ['使用微信公众号的AppId，不是开放平台的']
    }
  }
  
  const errorInfo = errorMap[errorCode] || {
    code: errorCode,
    message: '未知错误',
    solution: ['请查看微信官方文档或联系技术支持']
  }
  
  console.group(`❌ 微信授权错误 ${errorCode}`)
  console.error('错误码:', errorInfo.code)
  console.error('错误信息:', errorInfo.message)
  console.log('解决方案:')
  errorInfo.solution.forEach((solution, index) => {
    console.log(`  ${index + 1}. ${solution}`)
  })
  console.groupEnd()
  
  return errorInfo
}

/**
 * 一键诊断微信授权问题
 */
export function diagnoseWechatAuth() {
  console.group('🔧 微信授权问题诊断')
  
  // 检查基础配置
  const config = checkWechatAuthConfig()
  
  // 检查回调参数
  const callback = parseWechatAuthCallback()
  
  // 检查后台配置
  checkWechatBackendConfig()
  
  // 生成诊断报告
  const report = {
    timestamp: new Date().toISOString(),
    config,
    callback,
    recommendations: []
  }
  
  // 生成建议
  if (!config.appId || config.appId === 'YOUR_PROD_WECHAT_APPID') {
    report.recommendations.push('⚠️ 请配置正确的微信公众号AppId')
  }
  
  if (!config.isWechat) {
    report.recommendations.push('⚠️ 当前不在微信环境中，请在微信中测试')
  }
  
  if (callback.hasError) {
    report.recommendations.push(`❌ 授权失败: ${callback.error}`)
    if (callback.error === '10003') {
      report.recommendations.push('🔧 请检查微信公众号后台的授权域名配置')
    }
  }
  
  if (config.protocol === 'http:' && config.domain !== 'localhost') {
    report.recommendations.push('⚠️ 生产环境建议使用HTTPS')
  }
  
  console.log('📊 诊断报告:', report)
  console.groupEnd()
  
  return report
}
