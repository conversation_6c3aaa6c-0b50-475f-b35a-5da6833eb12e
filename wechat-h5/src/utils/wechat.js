/**
 * 微信相关工具函数
 */

/**
 * 检查是否在微信浏览器中
 */
export function isWechatBrowser() {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

/**
 * 检查是否在微信小程序中
 */
export function isWechatMiniProgram() {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('miniprogram')
}

/**
 * 获取微信版本
 */
export function getWechatVersion() {
  const ua = navigator.userAgent.toLowerCase()
  const match = ua.match(/micromessenger\/(\d+\.\d+\.\d+)/)
  return match ? match[1] : null
}

/**
 * 禁用微信分享
 */
export function disableWechatShare() {
  if (typeof WeixinJSBridge !== 'undefined') {
    WeixinJSBridge.call('hideOptionMenu')
  }
}

/**
 * 启用微信分享
 */
export function enableWechatShare() {
  if (typeof WeixinJSBridge !== 'undefined') {
    WeixinJSBridge.call('showOptionMenu')
  }
}

/**
 * 关闭微信窗口
 */
export function closeWechatWindow() {
  if (typeof WeixinJSBridge !== 'undefined') {
    WeixinJSBridge.call('closeWindow')
  } else {
    // 尝试关闭窗口
    window.close()
    
    // 如果无法关闭，提示用户
    setTimeout(() => {
      alert('请手动关闭此页面')
    }, 300)
  }
}

/**
 * 微信网页授权
 */
export function wechatWebAuth(options = {}) {
  const {
    appId,
    redirectUri,
    scope = 'snsapi_userinfo',
    state = Math.random().toString(36).substring(2)
  } = options
  
  if (!appId) {
    throw new Error('微信AppId不能为空')
  }
  
  if (!redirectUri) {
    throw new Error('回调地址不能为空')
  }
  
  // 保存state用于验证
  sessionStorage.setItem('wechat_auth_state', state)
  
  // 构建授权URL
  const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${encodeURIComponent(redirectUri)}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`
  
  // 跳转到授权页面
  window.location.href = authUrl
}

/**
 * 验证微信授权回调
 */
export function validateWechatAuthCallback() {
  const urlParams = new URLSearchParams(window.location.search)
  const code = urlParams.get('code')
  const state = urlParams.get('state')
  const savedState = sessionStorage.getItem('wechat_auth_state')
  
  // 检查是否有授权码
  if (!code) {
    throw new Error('未获取到微信授权码')
  }
  
  // 验证state参数
  if (state && savedState && state !== savedState) {
    throw new Error('授权验证失败，请重新授权')
  }
  
  return { code, state }
}

/**
 * 清理微信授权参数
 */
export function cleanWechatAuthParams() {
  // 清理URL参数
  const url = new URL(window.location.href)
  url.searchParams.delete('code')
  url.searchParams.delete('state')
  
  // 更新URL
  window.history.replaceState({}, document.title, url.toString())
  
  // 清理sessionStorage
  sessionStorage.removeItem('wechat_auth_state')
}

/**
 * 设置微信分享信息
 */
export function setWechatShare(shareInfo = {}) {
  const {
    title = document.title,
    desc = '得宝商城微信服务',
    link = window.location.href,
    imgUrl = ''
  } = shareInfo
  
  if (typeof wx !== 'undefined') {
    // 分享到朋友圈
    wx.onMenuShareTimeline({
      title,
      link,
      imgUrl
    })
    
    // 分享给朋友
    wx.onMenuShareAppMessage({
      title,
      desc,
      link,
      imgUrl
    })
  }
}

/**
 * 微信环境适配
 */
export function wechatEnvironmentAdapt() {
  // 禁用页面缩放
  document.addEventListener('touchstart', function (event) {
    if (event.touches.length > 1) {
      event.preventDefault()
    }
  })
  
  // 禁用双击缩放
  let lastTouchEnd = 0
  document.addEventListener('touchend', function (event) {
    const now = (new Date()).getTime()
    if (now - lastTouchEnd <= 300) {
      event.preventDefault()
    }
    lastTouchEnd = now
  }, false)
  
  // 禁用长按选择
  document.addEventListener('selectstart', function (event) {
    event.preventDefault()
  })
  
  // 禁用右键菜单
  document.addEventListener('contextmenu', function (event) {
    event.preventDefault()
  })
}
