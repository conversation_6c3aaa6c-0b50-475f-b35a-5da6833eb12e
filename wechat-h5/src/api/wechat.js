import { get, post } from '@/utils/request'

/**
 * 微信相关API接口
 */

// 通过微信网页授权code获取用户信息
export const getWechatUserInfoByCode = (code) => {
  return post('/store/wechat-bind/getUserInfo', { code })
}

// 确认微信绑定
export const confirmWechatBind = (params) => {
  return post('/store/wechat-bind/confirm', params)
}

// 检查绑定状态
export const checkWechatBindStatus = (bindToken, unionType) => {
  return get(`/store/wechat-bind/status`, {
    bindToken,
    unionType
  })
}
