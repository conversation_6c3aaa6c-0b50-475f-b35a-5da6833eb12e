# 得宝商城微信H5项目

## 项目简介

这是得宝商城的微信H5项目，专为微信用户访问优化，主要用于微信绑定确认等功能。

## 技术栈

- **框架**: Vue 2.6
- **UI组件**: Vant 2.x (移动端UI组件库)
- **状态管理**: Vuex
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vue CLI 4.x
- **样式预处理**: Less
- **移动端适配**: postcss-px-to-viewport

## 项目特性

- ✅ 移动端优先设计
- ✅ 微信浏览器环境检测
- ✅ 微信网页授权集成
- ✅ 响应式布局适配
- ✅ PWA支持
- ✅ Gzip压缩优化
- ✅ 代码分包优化

## 目录结构

```
wechat-h5/
├── public/                 # 静态资源
│   └── index.html         # HTML模板
├── src/
│   ├── api/               # API接口
│   │   └── wechat.js      # 微信相关接口
│   ├── components/        # 公共组件
│   ├── router/            # 路由配置
│   │   └── index.js       # 路由定义
│   ├── store/             # 状态管理
│   │   └── index.js       # Vuex store
│   ├── styles/            # 全局样式
│   │   └── index.less     # 样式入口
│   ├── utils/             # 工具函数
│   │   ├── index.js       # 通用工具
│   │   └── request.js     # HTTP请求封装
│   ├── views/             # 页面组件
│   │   ├── BindConfirm.vue # 绑定确认页面
│   │   └── Error.vue      # 错误页面
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── .env.development       # 开发环境配置
├── .env.production        # 生产环境配置
├── babel.config.js        # Babel配置
├── package.json           # 项目依赖
└── vue.config.js          # Vue CLI配置
```

## 环境配置

### 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run serve
```

### 生产环境

```bash
# 构建生产版本
npm run build

# 构建测试版本
npm run build:test
```

## 环境变量配置

### .env.development
```bash
VUE_APP_API_BASE_URL = 'http://127.0.0.1:8889'
VUE_APP_WECHAT_APPID = 'YOUR_DEV_WECHAT_APPID'
```

### .env.production
```bash
VUE_APP_API_BASE_URL = 'https://store-api.dboss.pro'
VUE_APP_WECHAT_APPID = 'YOUR_PROD_WECHAT_APPID'
```

## 功能模块

### 1. 微信绑定确认

- **路径**: `/bind-confirm`
- **功能**: 微信用户绑定确认
- **参数**: 
  - `token`: 绑定令牌
  - `type`: 绑定类型 (WECHAT_OFFIACCOUNT_OPEN_ID | WECHAT_MP_OPEN_ID)

### 2. 错误页面

- **路径**: `/error`
- **功能**: 统一错误处理页面
- **参数**:
  - `type`: 错误类型 (env | network | auth)
  - `message`: 自定义错误消息

## 微信配置要求

### 1. 微信公众号配置

1. **授权回调域名配置**：
   - 登录微信公众平台
   - 进入"开发" -> "接口权限" -> "网页服务" -> "网页授权"
   - 配置授权回调域名（不需要协议头）

2. **AppId配置**：
   - 在环境变量中配置正确的微信公众号AppId

### 2. 后端接口要求

需要后端提供以下接口：

- `POST /store/wechat-bind/getUserInfo` - 获取微信用户信息
- `POST /store/wechat-bind/confirm` - 确认微信绑定

## 部署说明

### 1. 构建配置

项目支持多环境构建：

```bash
# 开发环境
npm run serve

# 测试环境构建
npm run build:test

# 生产环境构建
npm run build:prod
```

### 2. 服务器配置

建议使用Nginx作为Web服务器，配置示例：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /wechat/ {
        root /var/www/html;
        try_files $uri $uri/ /wechat/index.html;
    }
    
    location /api/ {
        proxy_pass http://backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 开发注意事项

1. **微信环境测试**: 需要在微信浏览器中测试
2. **HTTPS要求**: 生产环境必须使用HTTPS
3. **域名配置**: 确保域名已在微信公众号后台配置
4. **移动端适配**: 使用vw单位进行移动端适配

## 更新日志

- **v1.0.0**: 初始版本，支持微信绑定确认功能
