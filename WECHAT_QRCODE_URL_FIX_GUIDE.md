# 微信绑定二维码URL修复指南

## 问题描述

当前商家端生成的微信绑定二维码指向了错误的URL：
- **当前URL**: `https://store.dboss.pro/store-wechat-bind?token=xxx&type=WECHAT_MP_OPEN_ID`
- **期望URL**: `https://m.dboss.pro/bind-confirm?token=xxx&type=WECHAT_MP_OPEN_ID`

## 解决方案

### 🎯 方案1：修改后端配置（推荐）

#### 1.1 修改后端二维码生成逻辑

后端需要修改以下接口的URL生成逻辑：
- `GET /store/wechat-bind/qrcode/official-account`
- `GET /store/wechat-bind/qrcode/mini-program`

#### 1.2 配置环境变量

在后端配置文件中添加H5项目的基础URL：

```properties
# application-dev.properties (开发环境)
wechat.h5.base-url=http://localhost:8080

# application-prod.properties (生产环境)  
wechat.h5.base-url=https://m.dboss.pro

# application-test.properties (测试环境)
wechat.h5.base-url=https://test-m.dboss.pro
```

#### 1.3 修改二维码URL生成代码

```java
// 示例代码（具体实现根据后端技术栈调整）
@Value("${wechat.h5.base-url}")
private String wechatH5BaseUrl;

public String generateBindUrl(String token, String unionType) {
    return String.format("%s/bind-confirm?token=%s&type=%s", 
                        wechatH5BaseUrl, token, unionType);
}
```

### 🔄 方案2：前端重定向（临时解决方案）

如果暂时无法修改后端，我们已经在前端实现了重定向机制：

#### 2.1 重定向组件

已创建 `seller/src/views/shop/WechatBindRedirect.vue` 组件：
- 自动检测微信环境
- 解析URL参数
- 重定向到正确的H5页面
- 提供手动跳转选项

#### 2.2 路由配置

已在 `seller/src/router/router.js` 中添加重定向路由：
```javascript
{
  path: '/store-wechat-bind',
  name: 'WechatBindRedirect',
  meta: {
    requireAuth: false,
    title: "微信绑定跳转"
  },
  component: () => import('@/views/shop/WechatBindRedirect.vue')
}
```

#### 2.3 配置管理

创建了 `seller/src/config/wechat-h5.js` 配置文件：
- 环境自动检测
- URL构建工具函数
- 统一配置管理

## 配置说明

### 环境变量配置

在 `seller/.env` 文件中可以配置H5项目URL：

```bash
# 开发环境H5项目URL
VUE_APP_WECHAT_H5_DEV_URL=http://localhost:8080

# 生产环境H5项目URL  
VUE_APP_WECHAT_H5_PROD_URL=https://m.dboss.pro

# 测试环境H5项目URL
VUE_APP_WECHAT_H5_TEST_URL=https://test-m.dboss.pro
```

### URL映射规则

| 环境 | 商家端域名 | H5项目域名 |
|------|------------|------------|
| 开发 | localhost:8080 | localhost:8080 |
| 测试 | test-store.dboss.pro | test-m.dboss.pro |
| 生产 | store.dboss.pro | m.dboss.pro |

## 测试验证

### 1. 开发环境测试

```bash
# 启动seller项目
cd seller
npm run serve

# 启动wechat-h5项目  
cd wechat-h5
npm run serve

# 访问绑定页面，生成二维码
# 扫描二维码或直接访问二维码URL
```

### 2. 生产环境测试

1. 部署H5项目到 `https://m.dboss.pro`
2. 在商家端生成绑定二维码
3. 使用微信扫描二维码
4. 验证是否正确跳转到H5页面

## 部署清单

### 前端部署

- [ ] 部署wechat-h5项目到 `https://m.dboss.pro`
- [ ] 配置Nginx反向代理
- [ ] 配置HTTPS证书
- [ ] 测试H5页面访问

### 后端配置

- [ ] 修改二维码生成逻辑
- [ ] 配置H5项目基础URL
- [ ] 更新环境变量
- [ ] 重启后端服务

### 微信配置

- [ ] 更新微信公众号授权回调域名
- [ ] 配置 `m.dboss.pro` 域名
- [ ] 测试微信网页授权

## 监控和日志

### 1. 重定向监控

在重定向组件中添加埋点：
```javascript
// 记录重定向事件
console.log('微信绑定重定向:', {
  from: window.location.href,
  to: this.redirectUrl,
  userAgent: navigator.userAgent,
  timestamp: new Date().toISOString()
})
```

### 2. 错误监控

监控重定向失败的情况：
- 缺少必要参数
- 网络请求失败
- 页面跳转异常

## 回滚方案

如果新方案出现问题，可以快速回滚：

1. **后端回滚**: 恢复原有的URL生成逻辑
2. **前端回滚**: 移除重定向路由，恢复原有页面
3. **域名回滚**: 临时将H5项目部署到原域名下

## 注意事项

1. **缓存问题**: 二维码可能被缓存，需要清理缓存或等待过期
2. **兼容性**: 确保新URL在各种微信版本中都能正常工作
3. **性能**: 重定向会增加一次跳转，可能影响用户体验
4. **SEO**: 确保重定向使用正确的HTTP状态码

## 后续优化

1. **直接生成**: 最终目标是后端直接生成正确的H5 URL
2. **缓存优化**: 实现二维码缓存机制，减少重复生成
3. **监控完善**: 添加完整的监控和告警机制
4. **用户体验**: 优化重定向过程的用户体验
