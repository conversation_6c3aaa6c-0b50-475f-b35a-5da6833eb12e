/**
 * 微信H5项目配置
 */

// 根据环境获取H5项目的基础URL
export function getWechatH5BaseUrl() {
  const currentHost = window.location.hostname
  
  // 开发环境
  if (currentHost.includes('localhost') || currentHost.includes('127.0.0.1')) {
    return process.env.VUE_APP_WECHAT_H5_DEV_URL || 'http://localhost:8080'
  }
  
  // 生产环境
  if (currentHost.includes('store.dboss.pro')) {
    return process.env.VUE_APP_WECHAT_H5_PROD_URL || 'https://m.dboss.pro'
  }
  
  // 测试环境
  if (currentHost.includes('test') || currentHost.includes('staging')) {
    return process.env.VUE_APP_WECHAT_H5_TEST_URL || 'https://test-m.dboss.pro'
  }
  
  // 默认配置
  return 'https://m.dboss.pro'
}

// 构建微信绑定确认页面URL
export function buildWechatBindConfirmUrl(token, unionType) {
  const baseUrl = getWechatH5BaseUrl()
  return `${baseUrl}/bind-confirm?token=${token}&type=${unionType}`
}

// 微信H5项目路由配置
export const WECHAT_H5_ROUTES = {
  BIND_CONFIRM: '/bind-confirm',
  ERROR: '/error'
}

// 环境配置
export const WECHAT_H5_CONFIG = {
  // 开发环境配置
  development: {
    baseUrl: 'http://localhost:8080',
    apiUrl: 'http://127.0.0.1:8889'
  },
  
  // 测试环境配置
  test: {
    baseUrl: 'https://test-m.dboss.pro',
    apiUrl: 'https://test-store-api.dboss.pro'
  },
  
  // 生产环境配置
  production: {
    baseUrl: 'https://m.dboss.pro',
    apiUrl: 'https://store-api.dboss.pro'
  }
}

// 获取当前环境配置
export function getCurrentEnvConfig() {
  const currentHost = window.location.hostname
  
  if (currentHost.includes('localhost') || currentHost.includes('127.0.0.1')) {
    return WECHAT_H5_CONFIG.development
  }
  
  if (currentHost.includes('test') || currentHost.includes('staging')) {
    return WECHAT_H5_CONFIG.test
  }
  
  return WECHAT_H5_CONFIG.production
}
