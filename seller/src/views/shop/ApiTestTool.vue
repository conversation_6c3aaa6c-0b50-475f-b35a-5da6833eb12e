<template>
  <div class="api-test-tool">
    <Card>
      <div slot="title">微信绑定API接口测试工具</div>
      
      <div class="environment-info">
        <Alert show-icon>
          <span slot="desc">
            <strong>当前环境</strong>: {{ isDev ? '开发环境' : '生产环境' }}<br>
            <strong>API地址</strong>: {{ apiBaseUrl }}<br>
            <strong>完整接口前缀</strong>: {{ fullApiUrl }}
          </span>
        </Alert>
      </div>
      
      <Divider>接口测试</Divider>
      
      <div class="test-section">
        <Row :gutter="16">
          <Col span="8">
            <Card class="test-card">
              <div slot="title">1. 生成公众号二维码</div>
              <div class="test-info">
                <p><strong>接口</strong>: GET /store/wechat-bind/qrcode/official-account</p>
                <p><strong>说明</strong>: 生成微信公众号绑定二维码</p>
              </div>
              <Button 
                type="primary" 
                @click="testOfficialAccountQR" 
                :loading="loading.officialQR"
                long
              >
                测试接口
              </Button>
              <div v-if="results.officialQR" class="result-section">
                <Tag :color="results.officialQR.success ? 'success' : 'error'">
                  {{ results.officialQR.success ? '成功' : '失败' }}
                </Tag>
                <pre class="result-data">{{ JSON.stringify(results.officialQR.data, null, 2) }}</pre>
              </div>
            </Card>
          </Col>
          
          <Col span="8">
            <Card class="test-card">
              <div slot="title">2. 生成小程序二维码</div>
              <div class="test-info">
                <p><strong>接口</strong>: GET /store/wechat-bind/qrcode/mini-program</p>
                <p><strong>说明</strong>: 生成微信小程序绑定二维码</p>
              </div>
              <Button 
                type="primary" 
                @click="testMiniProgramQR" 
                :loading="loading.miniQR"
                long
              >
                测试接口
              </Button>
              <div v-if="results.miniQR" class="result-section">
                <Tag :color="results.miniQR.success ? 'success' : 'error'">
                  {{ results.miniQR.success ? '成功' : '失败' }}
                </Tag>
                <pre class="result-data">{{ JSON.stringify(results.miniQR.data, null, 2) }}</pre>
              </div>
            </Card>
          </Col>
          
          <Col span="8">
            <Card class="test-card">
              <div slot="title">3. 获取绑定状态</div>
              <div class="test-info">
                <p><strong>接口</strong>: GET /store/wechat-bind/bind-status</p>
                <p><strong>说明</strong>: 获取当前商家的绑定状态</p>
              </div>
              <Button 
                type="primary" 
                @click="testBindStatus" 
                :loading="loading.bindStatus"
                long
              >
                测试接口
              </Button>
              <div v-if="results.bindStatus" class="result-section">
                <Tag :color="results.bindStatus.success ? 'success' : 'error'">
                  {{ results.bindStatus.success ? '成功' : '失败' }}
                </Tag>
                <pre class="result-data">{{ JSON.stringify(results.bindStatus.data, null, 2) }}</pre>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
      
      <Divider>网络连接测试</Divider>
      
      <div class="network-test">
        <Button @click="testNetworkConnection" :loading="loading.network">
          测试网络连接
        </Button>
        <div v-if="networkResult" class="network-result">
          <Tag :color="networkResult.success ? 'success' : 'error'">
            {{ networkResult.success ? '连接正常' : '连接失败' }}
          </Tag>
          <span class="result-text">{{ networkResult.message }}</span>
        </div>
      </div>
      
      <Divider>操作说明</Divider>
      
      <div class="instructions">
        <h4>测试步骤：</h4>
        <ol>
          <li>首先点击"测试网络连接"确保能够访问后端API</li>
          <li>依次测试各个接口，查看返回结果</li>
          <li>如果接口测试通过，可以前往正式页面进行功能测试</li>
          <li>如果接口测试失败，请检查：
            <ul>
              <li>后端服务是否正常运行</li>
              <li>API地址配置是否正确</li>
              <li>网络连接是否正常</li>
              <li>认证token是否有效</li>
            </ul>
          </li>
        </ol>
        
        <h4>页面链接：</h4>
        <p>
          <a href="#/store/wechat-bind" target="_blank">微信绑定功能页面</a> |
          <a href="#/store/wechat-bind-test" target="_blank">功能测试页面</a>
        </p>
      </div>
    </Card>
  </div>
</template>

<script>
import { 
  generateWechatOfficialAccountQRCode, 
  generateWechatMiniProgramQRCode,
  getWechatBindStatus
} from '@/api/shops'
import { baseUrl } from '@/libs/axios'

export default {
  name: 'ApiTestTool',
  data() {
    return {
      loading: {
        officialQR: false,
        miniQR: false,
        bindStatus: false,
        network: false
      },
      results: {
        officialQR: null,
        miniQR: null,
        bindStatus: null
      },
      networkResult: null
    }
  },
  computed: {
    isDev() {
      return process.env.NODE_ENV === 'development'
    },
    apiBaseUrl() {
      return this.isDev ? 'http://127.0.0.1:8889' : 'https://store-api.dboss.pro'
    },
    fullApiUrl() {
      return baseUrl
    }
  },
  methods: {
    async testOfficialAccountQR() {
      this.loading.officialQR = true
      try {
        const response = await generateWechatOfficialAccountQRCode()
        this.results.officialQR = {
          success: true,
          data: response,
          timestamp: new Date().toLocaleString()
        }
        this.$Message.success('公众号二维码接口测试成功')
      } catch (error) {
        this.results.officialQR = {
          success: false,
          data: {
            error: error.message,
            response: error.response?.data || null,
            status: error.response?.status || null
          },
          timestamp: new Date().toLocaleString()
        }
        this.$Message.error('公众号二维码接口测试失败')
      } finally {
        this.loading.officialQR = false
      }
    },
    
    async testMiniProgramQR() {
      this.loading.miniQR = true
      try {
        const response = await generateWechatMiniProgramQRCode()
        this.results.miniQR = {
          success: true,
          data: response,
          timestamp: new Date().toLocaleString()
        }
        this.$Message.success('小程序二维码接口测试成功')
      } catch (error) {
        this.results.miniQR = {
          success: false,
          data: {
            error: error.message,
            response: error.response?.data || null,
            status: error.response?.status || null
          },
          timestamp: new Date().toLocaleString()
        }
        this.$Message.error('小程序二维码接口测试失败')
      } finally {
        this.loading.miniQR = false
      }
    },
    
    async testBindStatus() {
      this.loading.bindStatus = true
      try {
        const response = await getWechatBindStatus()
        this.results.bindStatus = {
          success: true,
          data: response,
          timestamp: new Date().toLocaleString()
        }
        this.$Message.success('绑定状态接口测试成功')
      } catch (error) {
        this.results.bindStatus = {
          success: false,
          data: {
            error: error.message,
            response: error.response?.data || null,
            status: error.response?.status || null
          },
          timestamp: new Date().toLocaleString()
        }
        this.$Message.error('绑定状态接口测试失败')
      } finally {
        this.loading.bindStatus = false
      }
    },
    
    async testNetworkConnection() {
      this.loading.network = true
      try {
        // 测试基础连接
        const response = await fetch(this.apiBaseUrl + '/store/wechat-bind/bind-status', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          }
        })
        
        if (response.ok || response.status === 401) {
          // 401表示需要认证，但连接正常
          this.networkResult = {
            success: true,
            message: `网络连接正常 (状态码: ${response.status})`,
            timestamp: new Date().toLocaleString()
          }
        } else {
          this.networkResult = {
            success: false,
            message: `连接异常 (状态码: ${response.status})`,
            timestamp: new Date().toLocaleString()
          }
        }
      } catch (error) {
        this.networkResult = {
          success: false,
          message: `连接失败: ${error.message}`,
          timestamp: new Date().toLocaleString()
        }
      } finally {
        this.loading.network = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.api-test-tool {
  padding: 20px;
  
  .environment-info {
    margin-bottom: 24px;
  }
  
  .test-section {
    margin-bottom: 24px;
    
    .test-card {
      height: 100%;
      
      .test-info {
        margin-bottom: 16px;
        
        p {
          margin: 4px 0;
          font-size: 13px;
          color: #666;
          
          strong {
            color: #333;
          }
        }
      }
      
      .result-section {
        margin-top: 16px;
        
        .result-data {
          margin-top: 8px;
          padding: 8px;
          background: #f8f9fa;
          border: 1px solid #e8e8e8;
          border-radius: 4px;
          font-size: 11px;
          max-height: 200px;
          overflow-y: auto;
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
    }
  }
  
  .network-test {
    margin-bottom: 24px;
    
    .network-result {
      margin-top: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      
      .result-text {
        font-size: 14px;
        color: #333;
      }
    }
  }
  
  .instructions {
    h4 {
      margin-bottom: 12px;
      color: #333;
    }
    
    ol, ul {
      margin-left: 20px;
      
      li {
        margin: 8px 0;
        line-height: 1.6;
        
        ul {
          margin-top: 8px;
        }
      }
    }
    
    p {
      margin: 12px 0;
      
      a {
        color: #2d8cf0;
        text-decoration: none;
        
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

@media (max-width: 1200px) {
  .api-test-tool {
    .test-section {
      .ivu-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
