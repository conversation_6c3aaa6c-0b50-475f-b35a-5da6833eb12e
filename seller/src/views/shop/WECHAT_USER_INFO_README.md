# 微信用户信息获取功能说明

## 功能概述

`getWechatUserInfo()` 方法用于在微信浏览器环境中通过网页授权获取用户信息，主要用于微信绑定确认页面。

## 实现特性

- ✅ 使用微信网页授权 OAuth2.0 获取用户信息
- ✅ 支持获取 unionId、openId、昵称、头像等完整信息
- ✅ 自动处理授权流程，包括跳转和回调
- ✅ 完善的错误处理和用户友好的错误提示
- ✅ 兼容性检查，确保在微信浏览器环境中运行
- ✅ 安全的state参数验证，防止CSRF攻击

## 方法详解

### getWechatUserInfo()

**功能**: 获取微信网页授权用户信息

**返回值**: Promise&lt;UserInfo&gt;

```javascript
{
  unionId: string,    // 微信开放平台统一ID
  openId: string,     // 微信公众号openId
  nickname: string,   // 用户昵称
  avatar: string,     // 用户头像URL
  gender: number,     // 性别 (0-未知, 1-男, 2-女)
  city: string,       // 城市
  province: string,   // 省份
  country: string,    // 国家
  code: string        // 微信授权code
}
```

## 使用方式

```javascript
// 在 Vue 组件中使用
async confirmBind() {
  try {
    const userInfo = await this.getWechatUserInfo()
    console.log('用户信息:', userInfo)
    
    // 使用用户信息进行后续操作
    const response = await confirmWechatBind({
      unionId: userInfo.unionId,
      openId: userInfo.openId,
      nickname: userInfo.nickname,
      avatar: userInfo.avatar,
      // ... 其他参数
    })
  } catch (error) {
    console.error('获取用户信息失败:', error.message)
  }
}
```

## 错误处理

方法会处理以下错误情况：

1. **环境检查**: 检查是否在微信小程序环境中
2. **用户拒绝授权**: 提供友好的错误提示
3. **用户取消授权**: 区分取消和拒绝的情况
4. **网络错误**: 处理网络请求失败的情况

## 注意事项

1. **授权流程**: 微信网页授权需要用户在微信浏览器中访问
2. **回调处理**: 授权成功后会自动回调到当前页面，带上code和state参数
3. **环境要求**: 必须在微信浏览器环境中运行
4. **域名配置**: 需要在微信公众号后台配置授权回调域名
5. **AppId配置**: 需要配置正确的微信公众号AppId

## 微信公众号配置

1. **授权回调域名配置**：
   - 登录微信公众平台
   - 进入"开发" -> "接口权限" -> "网页服务" -> "网页授权"
   - 配置授权回调域名（不需要协议头，如：yourdomain.com）

2. **环境变量配置**：

```bash
# .env 文件
VUE_APP_WECHAT_APPID=your_wechat_appid
```

## 后端接口配置

### 1. 获取用户信息接口

**接口路径**: `POST /store/wechat-bind/getUserInfo`

**请求参数**:
```javascript
{
  code: string  // 微信授权code
}
```

**响应格式**:
```javascript
{
  success: boolean,
  result: {
    unionid: string,      // 微信unionId
    openid: string,       // 微信openId
    nickname: string,     // 用户昵称
    headimgurl: string,   // 用户头像URL
    sex: number,          // 性别 (0-未知, 1-男, 2-女)
    city: string,         // 城市
    province: string,     // 省份
    country: string       // 国家
  }
}
```

### 2. 确认绑定接口参数

后端确认绑定接口应该能够处理以下参数：

```javascript
{
  bindToken: string,    // 绑定令牌
  unionType: string,    // 绑定类型
  unionId: string,      // 微信unionId
  openId: string,       // 微信openId
  code: string,         // 微信授权code
  nickname: string,     // 用户昵称
  avatar: string,       // 用户头像
  gender: number,       // 性别
  city: string,         // 城市
  province: string,     // 省份
  country: string       // 国家
}
```

## 更新日志

- **v2.0.0**: 重构为微信网页授权方式
  - 支持微信浏览器环境中的网页授权
  - 完整的OAuth2.0授权流程
  - 安全的state参数验证
- **v1.0.0**: 初始实现（微信小程序版本）
