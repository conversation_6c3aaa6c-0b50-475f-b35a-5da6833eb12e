<template>
  <div class="wechat-bind-redirect">
    <div class="redirect-container">
      <div class="loading-section">
        <Spin size="large" />
        <p class="loading-text">正在跳转到微信绑定页面...</p>
      </div>
      
      <div class="info-section">
        <Alert type="info" show-icon>
          <div slot="desc">
            <p>检测到您正在使用微信扫码绑定功能</p>
            <p>系统将自动跳转到移动端优化页面</p>
          </div>
        </Alert>
      </div>
      
      <div class="manual-section" v-if="showManualLink">
        <p>如果页面没有自动跳转，请点击下方链接：</p>
        <Button type="primary" @click="manualRedirect">
          手动跳转到绑定页面
        </Button>
      </div>
    </div>
  </div>
</template>

<script>
import { buildWechatBindConfirmUrl } from '@/config/wechat-h5'

export default {
  name: 'WechatBindRedirect',
  data() {
    return {
      showManualLink: false,
      redirectUrl: ''
    }
  },
  mounted() {
    this.handleRedirect()
    
    // 5秒后显示手动跳转链接
    setTimeout(() => {
      this.showManualLink = true
    }, 5000)
  },
  methods: {
    handleRedirect() {
      try {
        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search)
        const token = urlParams.get('token')
        const type = urlParams.get('type')
        
        if (!token || !type) {
          this.$Message.error('缺少必要的绑定参数')
          return
        }
        
        // 构建新的H5页面URL
        this.redirectUrl = buildWechatBindConfirmUrl(token, type)
        
        // 检测是否在微信浏览器中
        const isWechat = /micromessenger/i.test(navigator.userAgent)
        
        if (isWechat) {
          // 在微信中，直接跳转
          this.performRedirect()
        } else {
          // 非微信环境，提示用户
          this.$Message.warning('请在微信中打开此页面')
          setTimeout(() => {
            this.performRedirect()
          }, 2000)
        }
        
      } catch (error) {
        console.error('重定向处理失败:', error)
        this.$Message.error('页面跳转失败')
      }
    },
    
    performRedirect() {
      if (this.redirectUrl) {
        console.log('重定向到:', this.redirectUrl)
        window.location.href = this.redirectUrl
      }
    },
    
    manualRedirect() {
      this.performRedirect()
    }
  }
}
</script>

<style lang="scss" scoped>
.wechat-bind-redirect {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.redirect-container {
  max-width: 500px;
  width: 100%;
  background: white;
  border-radius: 12px;
  padding: 40px 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.loading-section {
  margin-bottom: 30px;
  
  .loading-text {
    margin-top: 20px;
    font-size: 16px;
    color: #666;
    font-weight: 500;
  }
}

.info-section {
  margin-bottom: 30px;
  text-align: left;
  
  p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.5;
  }
}

.manual-section {
  padding-top: 20px;
  border-top: 1px solid #eee;
  
  p {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .redirect-container {
    padding: 30px 20px;
    margin: 10px;
  }
  
  .loading-section .loading-text {
    font-size: 14px;
  }
}

// 微信浏览器适配
@media screen and (max-width: 414px) {
  .wechat-bind-redirect {
    padding: 10px;
  }
  
  .redirect-container {
    border-radius: 8px;
    padding: 25px 15px;
  }
}
</style>
