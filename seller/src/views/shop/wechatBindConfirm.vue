<template>
  <div class="wechat-bind-confirm">
    <div class="confirm-container">
      <div class="logo-area">
        <Icon type="logo-wechat" size="60" color="#07c160" />
        <h1>微信通知绑定确认</h1>
      </div>

      <div v-if="loading" class="loading-section">
        <Spin size="large" fix></Spin>
        <p class="loading-text">正在处理，请稍候...</p>
      </div>

      <div v-else-if="error" class="error-section">
        <Icon type="ios-close-circle" size="60" color="#ed4014" />
        <h2>{{ errorMessage }}</h2>
        <p>{{ errorDetail }}</p>
        <Button type="primary" @click="closeWindow">关闭页面</Button>
      </div>

      <div v-else-if="!confirmed" class="confirm-section">
        <div class="bind-info">
          <p class="bind-type">
            您正在绑定{{ unionType === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序' }}
          </p>
          <p class="bind-desc">绑定后，您将可以接收订单付款等重要通知</p>
        </div>
        <div class="action-buttons">
          <Button @click="cancelBind" class="cancel-btn">取消</Button>
          <Button type="primary" @click="confirmBind" :loading="confirming">确认绑定</Button>
        </div>
      </div>

      <div v-else class="success-section">
        <Icon type="ios-checkmark-circle" size="60" color="#19be6b" />
        <h2>绑定成功</h2>
        <p>您已成功绑定{{ unionType === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序' }}</p>
        <Button type="primary" @click="closeWindow">关闭页面</Button>
      </div>
    </div>


    <div>
      响应：{{ result }}
    </div>
  </div>
</template>

<script>
import { confirmWechatBind, getWechatUserInfoByCode } from '@/api/shops'


export default {
  name: 'WechatBindConfirm',
  data() {
    return {
      token: '',
      unionType: '',
      loading: true,
      error: false,
      errorMessage: '绑定失败',
      errorDetail: '请重新扫描二维码',
      confirmed: false,
      confirming: false,
      result :null
    }
  },
  mounted() {
    // 从URL参数中获取token和type
    this.token = this.$route.query.token || ''
    this.unionType = this.$route.query.type || ''
    
    // 验证参数
    if (!this.token || !this.unionType) {
      this.loading = false
      this.error = true
      this.errorMessage = '参数错误'
      this.errorDetail = '缺少必要的绑定参数，请重新扫描二维码'
      return
    }
    
    // 验证unionType
    if (!['WECHAT_OFFIACCOUNT_OPEN_ID', 'WECHAT_MP_OPEN_ID'].includes(this.unionType)) {
      this.loading = false
      this.error = true
      this.errorMessage = '参数错误'
      this.errorDetail = '绑定类型不正确，请重新扫描二维码'
      return
    }
    
    // 完成加载
    this.loading = false
  },
  methods: {
    // 获取微信用户信息（网页授权）
    async getWechatUserInfo() {
      return new Promise((resolve, reject) => {
        // 检查是否在微信浏览器环境中
        if (!this.isWechatBrowser()) {
          reject(new Error('当前环境不是微信浏览器'))
          return
        }

        // 检查URL中是否已有授权code
        const urlParams = new URLSearchParams(window.location.search)
        const code = urlParams.get('code')
        const state = urlParams.get('state')

        if (code) {
          // 如果已有code，直接使用code获取用户信息
          this.getUserInfoByCode(code)
            .then(userInfo => resolve(userInfo))
            .catch(err => reject(err))
        } else {
          // 如果没有code，跳转到微信授权页面
          this.redirectToWechatAuth()
          reject(new Error('正在跳转到微信授权页面...'))
        }
      })
    },

    // 检查是否在微信浏览器中
    isWechatBrowser() {
      const ua = navigator.userAgent.toLowerCase()
      return ua.includes('micromessenger')
    },

    // 跳转到微信网页授权
    redirectToWechatAuth() {
      // 构建当前页面URL作为回调地址
      const currentUrl = window.location.href.split('?')[0]
      const redirectUri = encodeURIComponent(currentUrl)

      // 生成state参数用于安全验证
      const state = this.generateState()

      // 微信网页授权URL（需要配置appId）
      // 注意：这里需要在后端配置实际的微信公众号appId
      // 或者从环境变量/配置文件中读取
      const appId = process.env.VUE_APP_WECHAT_APPID || 'YOUR_WECHAT_APPID' // 需要配置实际的appId
      const scope = 'snsapi_userinfo' // 获取用户信息授权

      const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`

      // 保存state到sessionStorage用于验证
      sessionStorage.setItem('wechat_auth_state', state)

      // 跳转到微信授权页面
      window.location.href = authUrl
    },

    // 生成随机state参数
    generateState() {
      return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
    },

    // 通过code获取用户信息
    async getUserInfoByCode(code) {
      try {
        // 验证state参数
        const urlParams = new URLSearchParams(window.location.search)
        const state = urlParams.get('state')
        const savedState = sessionStorage.getItem('wechat_auth_state')

        if (state !== savedState) {
          throw new Error('授权验证失败，请重新授权')
        }

        // 调用后端API获取用户信息
        const response = await this.fetchWechatUserInfo(code)

        if (response.success) {
          const userInfo = response.result

          // 清理URL中的授权参数
          this.cleanAuthParams()

          return {
            unionId: userInfo.unionid || '',
            openId: userInfo.openid || '',
            nickname: userInfo.nickname || '微信用户',
            avatar: userInfo.headimgurl || '',
            gender: userInfo.sex || 0,
            city: userInfo.city || '',
            province: userInfo.province || '',
            country: userInfo.country || '',
            code: code
          }
        } else {
          throw new Error(response.message || '获取用户信息失败')
        }
      } catch (error) {
        console.error('通过code获取用户信息失败:', error)
        throw error
      }
    },

    // 调用后端API获取微信用户信息
    async fetchWechatUserInfo(code) {
      try {
        // 调用后端API获取微信用户信息
        const response = await getWechatUserInfoByCode(code)
        return response
      } catch (error) {
        console.error('调用后端API失败:', error)
        throw new Error('网络请求失败')
      }
    },

    // 清理URL中的授权参数
    cleanAuthParams() {
      const url = new URL(window.location.href)
      url.searchParams.delete('code')
      url.searchParams.delete('state')

      // 使用replaceState避免页面刷新
      window.history.replaceState({}, document.title, url.toString())

      // 清理sessionStorage
      sessionStorage.removeItem('wechat_auth_state')
    },

    // 确认绑定
    async confirmBind() {
      this.confirming = true
      try {
        // 获取微信用户信息
        const userInfo = await this.getWechatUserInfo()

        // 调用确认绑定API
        const response = await confirmWechatBind({
          bindToken: this.token,
          unionType: this.unionType,
          unionId: userInfo.unionId,      // unionId
          openId: userInfo.openId,        // openId
          code: userInfo.code,            // 微信登录code
          nickname: userInfo.nickname,    // 昵称
          avatar: userInfo.avatar,        // 头像
          gender: userInfo.gender,        // 性别
          city: userInfo.city,            // 城市
          province: userInfo.province,    // 省份
          country: userInfo.country       // 国家
        })

        this.result = response

        if (response.success) {
          this.confirmed = true
        } else {
          this.error = true
          this.errorMessage = '绑定失败'
          this.errorDetail = response.message || '请重新扫描二维码'
        }
      } catch (error) {
        console.error('确认绑定失败:', error)
        this.error = true
        this.errorMessage = '绑定失败'
        this.errorDetail = error.message || '网络错误或服务器异常，请重试'
      } finally {
        this.confirming = false
      }
    },
    
    // 取消绑定
    cancelBind() {
      this.error = true
      this.errorMessage = '已取消绑定'
      this.errorDetail = '您已取消绑定操作'
    },
    
    // 关闭窗口
    closeWindow() {
      // 尝试关闭窗口，如果是在微信内打开的页面
      if (typeof WeixinJSBridge !== 'undefined') {
        WeixinJSBridge.call('closeWindow')
      } else {
        window.close()
        // 如果无法关闭窗口，提示用户手动关闭
        setTimeout(() => {
          alert('请手动关闭此页面')
        }, 300)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.wechat-bind-confirm {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  padding: 20px;
  
  .confirm-container {
    width: 100%;
    max-width: 500px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 30px;
    text-align: center;
    
    .logo-area {
      margin-bottom: 30px;
      
      h1 {
        margin-top: 15px;
        font-size: 24px;
        color: #333;
      }
    }
    
    .loading-section {
      padding: 40px 0;
      
      .loading-text {
        margin-top: 20px;
        color: #666;
      }
    }
    
    .error-section, .success-section {
      padding: 20px 0;
      
      h2 {
        margin: 15px 0;
        font-size: 20px;
      }
      
      p {
        color: #666;
        margin-bottom: 25px;
      }
    }
    
    .confirm-section {
      padding: 20px 0;
      
      .bind-info {
        margin-bottom: 30px;
        
        .bind-type {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        
        .bind-desc {
          color: #666;
        }
      }
      
      .action-buttons {
        display: flex;
        justify-content: center;
        gap: 20px;
        
        .cancel-btn {
          margin-right: 10px;
        }
      }
    }
  }
}

// 微信风格
:deep(.ivu-btn-primary) {
  background-color: #07c160;
  border-color: #07c160;
  
  &:hover, &:focus {
    background-color: #06ad56;
    border-color: #06ad56;
  }
}

// 响应式调整
@media (max-width: 576px) {
  .wechat-bind-confirm {
    padding: 10px;
    
    .confirm-container {
      padding: 20px;
      
      .logo-area h1 {
        font-size: 20px;
      }
    }
  }
}
</style>