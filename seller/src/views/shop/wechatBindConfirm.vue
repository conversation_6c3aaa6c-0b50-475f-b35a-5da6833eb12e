<template>
  <div class="wechat-bind-confirm">
    <div class="confirm-container">
      <div class="logo-area">
        <Icon type="logo-wechat" size="60" color="#07c160" />
        <h1>微信通知绑定确认</h1>
      </div>

      <div v-if="loading" class="loading-section">
        <Spin size="large" fix></Spin>
        <p class="loading-text">正在处理，请稍候...</p>
      </div>

      <div v-else-if="error" class="error-section">
        <Icon type="ios-close-circle" size="60" color="#ed4014" />
        <h2>{{ errorMessage }}</h2>
        <p>{{ errorDetail }}</p>
        <Button type="primary" @click="closeWindow">关闭页面</Button>
      </div>

      <div v-else-if="!confirmed" class="confirm-section">
        <div class="bind-info">
          <p class="bind-type">
            您正在绑定{{ unionType === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序' }}
          </p>
          <p class="bind-desc">绑定后，您将可以接收订单付款等重要通知</p>
        </div>
        <div class="action-buttons">
          <Button @click="cancelBind" class="cancel-btn">取消</Button>
          <Button type="primary" @click="confirmBind" :loading="confirming">确认绑定</Button>
        </div>
      </div>

      <div v-else class="success-section">
        <Icon type="ios-checkmark-circle" size="60" color="#19be6b" />
        <h2>绑定成功</h2>
        <p>您已成功绑定{{ unionType === 'WECHAT_OFFIACCOUNT_OPEN_ID' ? '微信公众号' : '微信小程序' }}</p>
        <Button type="primary" @click="closeWindow">关闭页面</Button>
      </div>
    </div>


    <div>
      响应：{{ result }}
    </div>
  </div>
</template>

<script>
import { confirmWechatBind } from '@/api/shops'


export default {
  name: 'WechatBindConfirm',
  data() {
    return {
      token: '',
      unionType: '',
      loading: true,
      error: false,
      errorMessage: '绑定失败',
      errorDetail: '请重新扫描二维码',
      confirmed: false,
      confirming: false,
      result :null
    }
  },
  mounted() {
    // 从URL参数中获取token和type
    this.token = this.$route.query.token || ''
    this.unionType = this.$route.query.type || ''
    
    // 验证参数
    if (!this.token || !this.unionType) {
      this.loading = false
      this.error = true
      this.errorMessage = '参数错误'
      this.errorDetail = '缺少必要的绑定参数，请重新扫描二维码'
      return
    }
    
    // 验证unionType
    if (!['WECHAT_OFFIACCOUNT_OPEN_ID', 'WECHAT_MP_OPEN_ID'].includes(this.unionType)) {
      this.loading = false
      this.error = true
      this.errorMessage = '参数错误'
      this.errorDetail = '绑定类型不正确，请重新扫描二维码'
      return
    }
    
    // 完成加载
    this.loading = false
  },
  methods: {
    // 确认绑定
    async confirmBind() {
      this.confirming = true
      try {
        // 调用确认绑定API
        const response = await confirmWechatBind({
          bindToken: this.token,
          unionType: this.unionType
        })
        this.result = response

        
        if (response.success) {
          this.confirmed = true
        } else {
          this.error = true
          this.errorMessage = '绑定失败'
          this.errorDetail = response.message || '请重新扫描二维码'
        }
      } catch (error) {
        console.error('确认绑定失败:', error)
        this.error = true
        this.errorMessage = '绑定失败'
        this.errorDetail = error.message || '网络错误或服务器异常，请重试'
      } finally {
        this.confirming = false
      }
    },
    
    // 取消绑定
    cancelBind() {
      this.error = true
      this.errorMessage = '已取消绑定'
      this.errorDetail = '您已取消绑定操作'
    },
    
    // 关闭窗口
    closeWindow() {
      // 尝试关闭窗口，如果是在微信内打开的页面
      if (typeof WeixinJSBridge !== 'undefined') {
        WeixinJSBridge.call('closeWindow')
      } else {
        window.close()
        // 如果无法关闭窗口，提示用户手动关闭
        setTimeout(() => {
          alert('请手动关闭此页面')
        }, 300)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.wechat-bind-confirm {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f8f8;
  padding: 20px;
  
  .confirm-container {
    width: 100%;
    max-width: 500px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 30px;
    text-align: center;
    
    .logo-area {
      margin-bottom: 30px;
      
      h1 {
        margin-top: 15px;
        font-size: 24px;
        color: #333;
      }
    }
    
    .loading-section {
      padding: 40px 0;
      
      .loading-text {
        margin-top: 20px;
        color: #666;
      }
    }
    
    .error-section, .success-section {
      padding: 20px 0;
      
      h2 {
        margin: 15px 0;
        font-size: 20px;
      }
      
      p {
        color: #666;
        margin-bottom: 25px;
      }
    }
    
    .confirm-section {
      padding: 20px 0;
      
      .bind-info {
        margin-bottom: 30px;
        
        .bind-type {
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        
        .bind-desc {
          color: #666;
        }
      }
      
      .action-buttons {
        display: flex;
        justify-content: center;
        gap: 20px;
        
        .cancel-btn {
          margin-right: 10px;
        }
      }
    }
  }
}

// 微信风格
:deep(.ivu-btn-primary) {
  background-color: #07c160;
  border-color: #07c160;
  
  &:hover, &:focus {
    background-color: #06ad56;
    border-color: #06ad56;
  }
}

// 响应式调整
@media (max-width: 576px) {
  .wechat-bind-confirm {
    padding: 10px;
    
    .confirm-container {
      padding: 20px;
      
      .logo-area h1 {
        font-size: 20px;
      }
    }
  }
}
</style>