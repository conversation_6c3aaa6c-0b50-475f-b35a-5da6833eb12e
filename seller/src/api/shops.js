// 统一请求路径前缀在libs/axios.js中修改
import {getRequest, postRequest, putRequest, deleteRequest, importRequest, getRequestWithNoToken} from '@/libs/axios';


// 获取数据字典
export const getDictData = "/dictData/getByType/"
// Websocket
export const ws = "/ws"


//查询店铺列表
export const getShopListData = (params) => {
  return getRequest('/shop', params)
}
// 获取结算单分页
export const getBillPage = (params) => {
  return getRequest(`/order/bill/getByPage`, params)
}

// 获取商家结算单流水分页
export const getSellerFlow = (id, params) => {
  return getRequest(`/order/bill/${id}/getStoreFlow`, params)
}

// 商家核对结算单
export const reconciliation = (id, params) => {
  return putRequest(`/order/bill/check/${id}/`, params)
}
//根据id获取店铺信息
export const downloadBill = (id) => {
  return getRequest(`/order/bill/downLoad/${id}`, {}, 'blob')
}

// 获取商家分销订单流水分页
export const getDistributionFlow = (id, params) => {
  return getRequest(`/order/bill/${id}/getDistributionFlow`, params)
}

// 获取商家结算单详细
export const getBillDetail = (id, params) => {
  return getRequest(`/order/bill/get/${id}`, params)
}

// 获取所有物流公司
export const getLogistics = (id, params) => {
  return getRequest(`/other/logistics`, params)
}
//返回信息
export const getIsCheck =(logisticsId) =>{
  return getRequest(`other/logistics/${logisticsId}/getStoreLogistics`)
}

// 开启物流公司
export const logisticsChecked = (id, params) => {
  return postRequest(`/other/logistics/${id}`, params, {
    "Content-type": "application/json"
  })
}
//获取发货地址
export const getDeliverAddress = () =>{
  return getRequest(`/settings/storeSettings/storeDeliverGoodsAddress`)
}
//修改发货地址
export const editDeliverAddress = (params) =>{
  return putRequest(`/settings/storeSettings/storeDeliverGoodsAddress`,params)
}

// 关闭开启物流公司
export const logisticsUnChecked = (id, params) => {
  return deleteRequest(`/other/logistics/${id}`, params)
}
// 获取商家自提点
export const getShopAddress = (id, params) => {
  return getRequest(`/member/storeAddress/`, params)
}

// 修改商家自提点
export const editShopAddress = (id, params) => {
  return putRequest(`/member/storeAddress/${id}`, params)
}

// 添加商品自提点
export const addShopAddress = (params) => {
  return postRequest(`/member/storeAddress/`, params)
}

// 添加商品自提点
export const deleteShopAddress = (id) => {
  return deleteRequest(`/member/storeAddress/${id}`)
}

// 获取商家详细信息
export const getShopInfo = () => {
  return getRequest(`/settings/storeSettings`)
}

// 保存商家详细信息
export const saveShopInfo = (params) => {
  return putRequest(`/settings/storeSettings`, params)
}

//获取商家退货地址
export const getRefundGoodsAddress = () => {
  return getRequest(`/settings/storeSettings/storeAfterSaleAddress`)
}
//修改商家退货地址
export const saveRefundGoodsAddress = (params) => {
  return putRequest(`/settings/storeSettings/storeAfterSaleAddress`, params)
}
//修改im商户id
export const updatEmerchantId = (params) => {
  return putRequest(`/settings/storeSettings/merchantEuid`, params)
}


//修改保存库存预警数
export const updateStockWarning = (params) => {
  return putRequest(`/settings/storeSettings/updateStockWarning`, params)
}
//查询运费模板
export const getShipTemplate = () => {
  return getRequest(`/setting/freightTemplate`)
}
//删除运费模板
export const deleteShipTemplate = (id) => {
  return deleteRequest(`/setting/freightTemplate/${id}`)
}
//新增运费模板
export const addShipTemplate = (params, headers) => {
  return postRequest(`/setting/freightTemplate`, params, headers)
}

//新增运费模板
export const editShipTemplate = (id, params, headers) => {
  return putRequest(`/setting/freightTemplate/${id}`, params, headers)
}

//修改电子面单等信息
export const editChecked = (logisticsId,params) => {
  return putRequest(`/other/logistics/${logisticsId}/updateStoreLogistics`,params)
}

// ==================== 微信绑定相关API ====================

// 确认微信绑定
export const confirmWechatBind = (params) => {
  return postRequest('/wechat-bind/confirm', params);
}

// 生成微信公众号绑定二维码
export const generateWechatOfficialAccountQRCode = () => {
  return getRequest('/wechat-bind/qrcode/official-account')
}

// 生成微信小程序绑定二维码
export const generateWechatMiniProgramQRCode = () => {
  return getRequest('/wechat-bind/qrcode/mini-program')
}

// 检查绑定状态
export const checkWechatBindStatus = (bindToken, unionType) => {
  return getRequest(`/wechat-bind/status?bindToken=${bindToken}&unionType=${unionType}`)
}

// 获取商家绑定状态
export const getWechatBindStatus = () => {
  return getRequest('/wechat-bind/bind-status')
}

// 更新通知状态
export const updateWechatNotification = (unionType, enable) => {
  return putRequest(`/connect/notification?unionType=${unionType}&enable=${enable}`)
}

// 解除绑定
export const unbindWechat = (unionType) => {
  return deleteRequest(`/wechat-bind/unbind?unionType=${unionType}`)
}

// 通过微信网页授权code获取用户信息
export const getWechatUserInfoByCode = (code) => {
  return postRequest('/wechat-bind/getUserInfo', { code })
}
