<svg height="16" viewBox="0 0 1024 1024" width="16" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景圆 -->
  <path d="m0 512a512 512 0 1 0 1024 0 512 512 0 1 0 -1024 0z" fill="#fff"/>
  
  <!-- 主体圆 -->
  <path d="m512 39.385a472.615 472.615 0 1 0 472.615 472.615 472.615 472.615 0 0 0 -472.615-472.615z" fill="#07c160"/>
  
  <!-- 微信图标主体 -->
  <g transform="translate(200, 200) scale(0.6)">
    <!-- 左侧对话气泡 -->
    <path d="M300 200c-80 0-150 50-150 120 0 40 20 75 50 95l-20 40 45-25c25 10 50 15 75 15 5 0 10 0 15-1-5-15-5-30-5-45 0-70 60-125 135-125 10 0 20 1 30 3-15-40-60-77-175-77z" fill="#fff"/>
    
    <!-- 右侧对话气泡 -->
    <path d="M580 280c-70 0-125 45-125 100 0 55 55 100 125 100 20 0 40-5 55-10l35 20-15-30c25-15 40-40 40-65 0-55-55-115-115-115z" fill="#fff"/>
    
    <!-- 通知铃铛 -->
    <g transform="translate(450, 150) scale(0.8)">
      <path d="M50 30c0-15 15-30 30-30s30 15 30 30v10h-60v-10z" fill="#ffd700"/>
      <path d="M20 40h120c10 0 20 10 20 20v40c0 30-25 55-55 55h-50c-30 0-55-25-55-55v-40c0-10 10-20 20-20z" fill="#ffd700"/>
      <path d="M60 155c0 10 10 20 20 20s20-10 20-20h-40z" fill="#ffd700"/>
      <!-- 通知点 -->
      <circle cx="120" cy="50" r="15" fill="#ff4444"/>
    </g>
  </g>
</svg>
