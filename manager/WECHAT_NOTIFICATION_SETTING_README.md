# 微信通知端设置功能说明

## 功能概述

在信任登录-微信设置中新增了"微信通知端"配置项，用于存储商家微信服务的appId和appSecret，实现微信通知功能的配置管理。

## 修改内容

### 1. 前端界面修改

#### 1.1 添加微信通知端类型
在 `manager/src/views/sys/setting-manage/authLogin/WECHAT_CONNECT.vue` 中：

```javascript
way: { // 类型
  APP: "移动应用端",
  H5: "移动端", 
  WECHAT_MP: "小程序端",
  PC: "PC端",
  WECHAT_NOTIFICATION: "微信通知端", // 新增
},
```

#### 1.2 添加图标显示逻辑
```vue
<div class="icon-item" v-if="item.clientType== 'WECHAT_NOTIFICATION'">
  <!-- 微信通知端 -->
  <img class="icon" src="../../../../assets/setting/wechat_notification.svg" alt="" srcset="">
</div>
```

#### 1.3 创建微信通知端图标
新增文件：`manager/src/assets/setting/wechat_notification.svg`
- 基于微信设计风格
- 包含通知铃铛元素
- 与其他端图标保持一致的设计风格

#### 1.4 修复显示逻辑
移除了模板中未定义的 `client` 变量过滤条件，确保所有配置项正常显示。

### 2. 数据结构

微信通知端的数据结构与其他端保持一致：

```javascript
{
  clientType: "WECHAT_NOTIFICATION",
  appId: "微信公众号AppId",
  appSecret: "微信公众号AppSecret"
}
```

### 3. 存储方式

- **存储位置**: 与PC端、移动端、小程序端相同的数据结构中
- **API接口**: 使用相同的 `setSetting` 接口
- **数据字段**: `wechatConnectSettingItems`

## 使用说明

### 管理员操作步骤

1. **进入设置页面**
   - 登录管理后台
   - 进入"系统设置" → "信任登录" → "微信设置"

2. **配置微信通知端**
   - 找到"微信通知端"配置卡片
   - 输入微信公众号的AppId
   - 输入微信公众号的AppSecret
   - 点击"保存设置"

3. **验证配置**
   - 确认配置保存成功
   - 检查微信通知功能是否正常工作

### 配置项说明

| 字段 | 说明 | 示例 |
|------|------|------|
| AppId | 微信公众号应用ID | wx1234567890abcdef |
| AppSecret | 微信公众号应用密钥 | 1234567890abcdef1234567890abcdef |

## 技术实现

### 前端实现

1. **组件结构**
   ```
   WECHAT_CONNECT.vue
   ├── 模板循环显示各端配置
   ├── 图标显示逻辑
   ├── 表单输入组件
   └── 保存设置功能
   ```

2. **关键代码**
   ```vue
   <div class="row" v-for="(item,index) in formValidate" :key="index">
     <div class="icon-item" v-if="item.clientType== 'WECHAT_NOTIFICATION'">
       <img class="icon" src="../../../../assets/setting/wechat_notification.svg">
     </div>
     <div class='pay-title'>{{way[item.clientType]}}</div>
     <!-- 表单输入 -->
   </div>
   ```

3. **数据处理**
   ```javascript
   // 初始化数据
   this.formValidate = JSON.parse(this.res).wechatConnectSettingItems;
   
   // 保存设置
   setSetting(this.type, {
     wechatConnectSettingItems: this.formValidate,
   })
   ```

### 后端要求

后端需要支持 `WECHAT_NOTIFICATION` 客户端类型：

1. **数据模型**
   ```java
   public enum ClientTypeEnum {
       APP,
       PC, 
       H5,
       WECHAT_MP,
       WECHAT_NOTIFICATION  // 新增
   }
   ```

2. **配置存储**
   - 在微信连接设置中添加对应的配置项
   - 支持CRUD操作
   - 数据验证和安全性检查

## 注意事项

### 1. 安全性
- AppSecret为敏感信息，需要加密存储
- 前端显示时可考虑部分遮蔽
- 访问权限控制

### 2. 兼容性
- 确保与现有微信功能不冲突
- 向后兼容已有配置
- 数据迁移考虑

### 3. 验证
- AppId格式验证（wx开头，18位）
- AppSecret格式验证（32位字符串）
- 微信API连通性测试

## 扩展功能

### 可能的后续功能

1. **配置验证**
   - 实时验证AppId和AppSecret的有效性
   - 显示配置状态（已配置/未配置/配置错误）

2. **功能测试**
   - 提供测试按钮验证微信通知功能
   - 发送测试消息确认配置正确

3. **使用统计**
   - 显示微信通知发送统计
   - 配置使用情况监控

## 相关文件

### 修改的文件
- `manager/src/views/sys/setting-manage/authLogin/WECHAT_CONNECT.vue`

### 新增的文件  
- `manager/src/assets/setting/wechat_notification.svg`
- `manager/WECHAT_NOTIFICATION_SETTING_README.md`

### 相关文件
- `manager/src/views/sys/setting-manage/settingManage.vue`
- `manager/src/views/sys/setting-manage/template.js`
- `manager/src/api/index.js`

## 测试建议

1. **功能测试**
   - 验证微信通知端配置项显示正常
   - 测试AppId和AppSecret的保存和读取
   - 确认与其他端配置不冲突

2. **界面测试**
   - 检查图标显示效果
   - 验证响应式布局
   - 测试不同浏览器兼容性

3. **数据测试**
   - 验证数据格式正确性
   - 测试特殊字符处理
   - 确认数据持久化正常
